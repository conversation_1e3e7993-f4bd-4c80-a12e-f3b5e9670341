<?php
/**
 * Global Auto-Save Handler for Redco Optimizer
 *
 * Provides comprehensive auto-save functionality with robust error handling,
 * network failure recovery, and memory-efficient operations.
 *
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Global_Auto_Save_Handler {

    /**
     * Auto-save configuration
     */
    private static $config = array(
        'save_interval' => 45000, // 45 seconds
        'max_retries' => 3,
        'retry_delay' => 2000, // 2 seconds
        'batch_size' => 5, // Maximum fields to save in one request
        'memory_limit_threshold' => 0.8, // 80% of memory limit
        'network_timeout' => 15000, // 15 seconds
    );

    /**
     * Initialize the global auto-save handler
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_redco_global_auto_save', array(__CLASS__, 'handle_auto_save_request'));
        add_action('wp_ajax_redco_global_auto_save_batch', array(__CLASS__, 'handle_batch_save_request'));
        add_action('wp_ajax_redco_check_save_status', array(__CLASS__, 'handle_status_check'));
        
        // Add hooks for memory management
        add_action('wp_ajax_redco_global_auto_save', array(__CLASS__, 'check_memory_usage'), 1);
        add_action('wp_ajax_redco_global_auto_save_batch', array(__CLASS__, 'check_memory_usage'), 1);
    }

    /**
     * Handle individual auto-save request
     */
    public static function handle_auto_save_request() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        $field_name = sanitize_text_field($_POST['field_name'] ?? '');
        $field_value = $_POST['field_value'] ?? '';
        $form_data = $_POST['form_data'] ?? array();

        // Validate required parameters
        if (empty($module) || empty($field_name)) {
            wp_send_json_error(array(
                'message' => __('Missing required parameters.', 'redco-optimizer'),
                'code' => 'MISSING_PARAMS'
            ));
            return;
        }

        try {
            // Sanitize and validate the field value
            $sanitized_value = self::sanitize_field_value($field_value, $field_name, $module);
            
            // Get current module settings
            $current_settings = self::get_module_settings($module);
            
            // Preserve array structure for settings[field_name] format
            $field_key = self::extract_field_key($field_name);
            $current_settings[$field_key] = $sanitized_value;
            
            // Save the updated settings
            $success = self::save_module_settings($module, $current_settings);
            
            if ($success) {
                // Clear relevant caches efficiently
                self::clear_module_cache($module);
                
                wp_send_json_success(array(
                    'message' => __('Setting saved successfully.', 'redco-optimizer'),
                    'module' => $module,
                    'field' => $field_key,
                    'value' => $sanitized_value,
                    'timestamp' => current_time('mysql'),
                    'memory_usage' => self::get_memory_usage_info()
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to save setting.', 'redco-optimizer'),
                    'code' => 'SAVE_FAILED',
                    'module' => $module,
                    'field' => $field_key
                ));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('An error occurred while saving.', 'redco-optimizer'),
                'code' => 'EXCEPTION',
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Handle batch auto-save request for multiple fields
     */
    public static function handle_batch_save_request() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        $fields = $_POST['fields'] ?? array();

        // Validate required parameters
        if (empty($module) || !is_array($fields) || empty($fields)) {
            wp_send_json_error(array(
                'message' => __('Missing or invalid parameters.', 'redco-optimizer'),
                'code' => 'INVALID_BATCH_PARAMS'
            ));
            return;
        }

        // Limit batch size to prevent memory issues
        if (count($fields) > self::$config['batch_size']) {
            $fields = array_slice($fields, 0, self::$config['batch_size']);
        }

        try {
            // Get current module settings
            $current_settings = self::get_module_settings($module);
            $updated_fields = array();
            
            // Process each field in the batch
            foreach ($fields as $field_data) {
                if (!isset($field_data['name']) || !isset($field_data['value'])) {
                    continue;
                }
                
                $field_name = sanitize_text_field($field_data['name']);
                $field_value = $field_data['value'];
                
                // Sanitize and validate the field value
                $sanitized_value = self::sanitize_field_value($field_value, $field_name, $module);
                
                // Preserve array structure
                $field_key = self::extract_field_key($field_name);
                $current_settings[$field_key] = $sanitized_value;
                $updated_fields[$field_key] = $sanitized_value;
            }
            
            // Save all updated settings at once
            $success = self::save_module_settings($module, $current_settings);
            
            if ($success) {
                // Clear relevant caches efficiently
                self::clear_module_cache($module);
                
                wp_send_json_success(array(
                    'message' => sprintf(__('%d settings saved successfully.', 'redco-optimizer'), count($updated_fields)),
                    'module' => $module,
                    'fields' => $updated_fields,
                    'count' => count($updated_fields),
                    'timestamp' => current_time('mysql'),
                    'memory_usage' => self::get_memory_usage_info()
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to save settings batch.', 'redco-optimizer'),
                    'code' => 'BATCH_SAVE_FAILED',
                    'module' => $module
                ));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('An error occurred while saving batch.', 'redco-optimizer'),
                'code' => 'BATCH_EXCEPTION',
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Handle save status check request
     */
    public static function handle_status_check() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        
        if (empty($module)) {
            wp_send_json_error(array(
                'message' => __('Module parameter required.', 'redco-optimizer'),
                'code' => 'MISSING_MODULE'
            ));
            return;
        }

        try {
            // Get current module settings to verify they exist
            $settings = self::get_module_settings($module);
            
            wp_send_json_success(array(
                'module' => $module,
                'settings_count' => count($settings),
                'last_modified' => get_option('redco_optimizer_' . str_replace('-', '_', $module) . '_last_modified', ''),
                'memory_usage' => self::get_memory_usage_info(),
                'timestamp' => current_time('mysql')
            ));
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to check status.', 'redco-optimizer'),
                'code' => 'STATUS_CHECK_FAILED',
                'debug' => array(
                    'error' => $e->getMessage()
                )
            ));
        }
    }

    /**
     * Verify AJAX request security
     */
    private static function verify_request() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_global_auto_save_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security verification failed.', 'redco-optimizer'),
                'code' => 'NONCE_FAILED'
            ));
            return false;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Insufficient permissions.', 'redco-optimizer'),
                'code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return false;
        }

        return true;
    }

    /**
     * Check memory usage before processing
     */
    public static function check_memory_usage() {
        $memory_info = self::get_memory_usage_info();
        
        if ($memory_info['usage_percentage'] > self::$config['memory_limit_threshold']) {
            wp_send_json_error(array(
                'message' => __('Memory usage too high. Please try again later.', 'redco-optimizer'),
                'code' => 'MEMORY_LIMIT_EXCEEDED',
                'memory_info' => $memory_info
            ));
            return false;
        }
        
        return true;
    }

    /**
     * Get memory usage information
     */
    private static function get_memory_usage_info() {
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));

        return array(
            'current' => $memory_usage,
            'peak' => $memory_peak,
            'limit' => $memory_limit,
            'usage_percentage' => ($memory_usage / $memory_limit) * 100,
            'available' => $memory_limit - $memory_usage
        );
    }

    /**
     * Sanitize field value based on context
     */
    private static function sanitize_field_value($value, $field_name, $module) {
        // Handle different value types
        if (is_array($value)) {
            return array_map('sanitize_text_field', $value);
        }

        // Handle boolean-like values
        if (in_array($value, array('true', 'false', '1', '0'), true)) {
            return $value === 'true' || $value === '1' ? 1 : 0;
        }

        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? floatval($value) : intval($value);
        }

        // Handle URLs
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return esc_url_raw($value);
        }

        // Handle emails
        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return sanitize_email($value);
        }

        // Default text sanitization
        return sanitize_text_field($value);
    }

    /**
     * Extract field key from form field name (handles settings[field_name] format)
     */
    private static function extract_field_key($field_name) {
        // Handle settings[field_name] format
        if (preg_match('/^settings\[([^\]]+)\]$/', $field_name, $matches)) {
            return $matches[1];
        }

        // Handle other bracket formats
        if (preg_match('/^[^[]+\[([^\]]+)\]$/', $field_name, $matches)) {
            return $matches[1];
        }

        return $field_name;
    }

    /**
     * Get module settings with fallback to defaults
     */
    private static function get_module_settings($module) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Get defaults if available
        $defaults = array();
        if (class_exists('Redco_Config')) {
            $defaults = Redco_Config::get_module_defaults($module);
        }

        return get_option($option_name, $defaults);
    }

    /**
     * Save module settings with error handling
     */
    private static function save_module_settings($module, $settings) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Update last modified timestamp
        update_option($option_name . '_last_modified', current_time('mysql'));

        return update_option($option_name, $settings);
    }

    /**
     * Clear module cache efficiently
     */
    private static function clear_module_cache($module) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Clear WordPress object cache for this specific option only
        wp_cache_delete($option_name, 'options');

        // Clear module-specific transients only
        delete_transient('redco_' . $module . '_cache');
        delete_transient('redco_' . $module . '_settings');

        // Clear any module-specific caches
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('redco_' . $module);
        }
    }

    /**
     * Get auto-save configuration
     */
    public static function get_config() {
        return self::$config;
    }

    /**
     * Update auto-save configuration
     */
    public static function update_config($key, $value) {
        if (isset(self::$config[$key])) {
            self::$config[$key] = $value;
        }
    }
}

// Initialize the global auto-save handler
Redco_Global_Auto_Save_Handler::init();
