<?php
/**
 * Global Auto-Save Handler for Redco Optimizer
 *
 * Provides comprehensive auto-save functionality with robust error handling,
 * network failure recovery, and memory-efficient operations.
 *
 * @package RedcoOptimizer
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Redco_Global_Auto_Save_Handler {

    /**
     * Auto-save configuration
     */
    private static $config = array(
        'save_interval' => 45000, // 45 seconds
        'max_retries' => 3,
        'retry_delay' => 2000, // 2 seconds
        'batch_size' => 5, // Maximum fields to save in one request
        'memory_limit_threshold' => 0.95, // 95% of memory limit (much more permissive)
        'memory_limit_threshold_high_memory' => 0.98, // 98% for high-memory modules
        'network_timeout' => 15000, // 15 seconds
        'memory_intensive_modules' => array(), // Remove blanket module restrictions
        'memory_intensive_fields' => array('minify_css', 'minify_js', 'critical_css', 'combine_css', 'combine_js'),
        'queue_enabled' => true, // Enable queuing for memory-constrained environments
        'queue_retry_delay' => 5000, // 5 seconds delay for queued saves
        'force_direct_processing' => true, // Force direct processing for most operations
        'queue_only_critical' => true, // Only queue truly critical memory operations
    );

    /**
     * Initialize the global auto-save handler
     */
    public static function init() {
        // Register AJAX handlers
        add_action('wp_ajax_redco_global_auto_save', array(__CLASS__, 'handle_auto_save_request'));
        add_action('wp_ajax_redco_global_auto_save_batch', array(__CLASS__, 'handle_batch_save_request'));
        add_action('wp_ajax_redco_check_save_status', array(__CLASS__, 'handle_status_check'));
        
        // Memory management integrated into request handlers
        add_action('wp_ajax_redco_global_auto_save_batch', array(__CLASS__, 'check_memory_usage'), 1);

        // Add queue processing for memory-constrained saves
        add_action('wp_ajax_redco_process_auto_save_queue', array(__CLASS__, 'process_auto_save_queue'));

        // Add WordPress cron hook for queue processing
        add_action('redco_process_auto_save_queue', array(__CLASS__, 'process_auto_save_queue'));

        // Add immediate queue processing for disabled cron
        add_action('wp_ajax_redco_process_auto_save_queue_immediate', array(__CLASS__, 'ajax_process_queue_immediate'));
        add_action('wp_ajax_nopriv_redco_process_auto_save_queue_immediate', array(__CLASS__, 'ajax_process_queue_immediate'));
    }

    /**
     * Handle individual auto-save request
     */
    public static function handle_auto_save_request() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        $field_name = sanitize_text_field($_POST['field_name'] ?? '');
        $field_value = $_POST['field_value'] ?? '';
        $form_data = $_POST['form_data'] ?? array();

        // Validate required parameters
        if (empty($module) || empty($field_name)) {
            wp_send_json_error(array(
                'message' => __('Missing required parameters.', 'redco-optimizer'),
                'code' => 'MISSING_PARAMS'
            ));
            return;
        }

        // Check memory usage before processing
        if (!self::check_memory_usage_internal($module, $field_name)) {
            return; // Response already sent by check_memory_usage_internal
        }

        try {
            // Sanitize and validate the field value
            $sanitized_value = self::sanitize_field_value($field_value, $field_name, $module);
            
            // Get current module settings
            $current_settings = self::get_module_settings($module);
            
            // Preserve array structure for settings[field_name] format
            $field_key = self::extract_field_key($field_name);
            $current_settings[$field_key] = $sanitized_value;
            
            // Save the updated settings
            $success = self::save_module_settings($module, $current_settings);
            
            if ($success) {
                // Clear relevant caches efficiently with memory optimization
                self::clear_module_cache_optimized($module, $field_key);
                
                wp_send_json_success(array(
                    'message' => __('Setting saved successfully.', 'redco-optimizer'),
                    'module' => $module,
                    'field' => $field_key,
                    'value' => $sanitized_value,
                    'timestamp' => current_time('mysql'),
                    'memory_usage' => self::get_memory_usage_info()
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to save setting.', 'redco-optimizer'),
                    'code' => 'SAVE_FAILED',
                    'module' => $module,
                    'field' => $field_key
                ));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('An error occurred while saving.', 'redco-optimizer'),
                'code' => 'EXCEPTION',
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Handle batch auto-save request for multiple fields
     */
    public static function handle_batch_save_request() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        $fields = $_POST['fields'] ?? array();

        // Validate required parameters
        if (empty($module) || !is_array($fields) || empty($fields)) {
            wp_send_json_error(array(
                'message' => __('Missing or invalid parameters.', 'redco-optimizer'),
                'code' => 'INVALID_BATCH_PARAMS'
            ));
            return;
        }

        // Limit batch size to prevent memory issues
        if (count($fields) > self::$config['batch_size']) {
            $fields = array_slice($fields, 0, self::$config['batch_size']);
        }

        try {
            // Get current module settings
            $current_settings = self::get_module_settings($module);
            $updated_fields = array();
            
            // Process each field in the batch
            foreach ($fields as $field_data) {
                if (!isset($field_data['name']) || !isset($field_data['value'])) {
                    continue;
                }
                
                $field_name = sanitize_text_field($field_data['name']);
                $field_value = $field_data['value'];
                
                // Sanitize and validate the field value
                $sanitized_value = self::sanitize_field_value($field_value, $field_name, $module);
                
                // Preserve array structure
                $field_key = self::extract_field_key($field_name);
                $current_settings[$field_key] = $sanitized_value;
                $updated_fields[$field_key] = $sanitized_value;
            }
            
            // Save all updated settings at once
            $success = self::save_module_settings($module, $current_settings);
            
            if ($success) {
                // Clear relevant caches efficiently with memory optimization
                self::clear_module_cache_optimized($module, 'batch');
                
                wp_send_json_success(array(
                    'message' => sprintf(__('%d settings saved successfully.', 'redco-optimizer'), count($updated_fields)),
                    'module' => $module,
                    'fields' => $updated_fields,
                    'count' => count($updated_fields),
                    'timestamp' => current_time('mysql'),
                    'memory_usage' => self::get_memory_usage_info()
                ));
            } else {
                wp_send_json_error(array(
                    'message' => __('Failed to save settings batch.', 'redco-optimizer'),
                    'code' => 'BATCH_SAVE_FAILED',
                    'module' => $module
                ));
            }
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('An error occurred while saving batch.', 'redco-optimizer'),
                'code' => 'BATCH_EXCEPTION',
                'debug' => array(
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                )
            ));
        }
    }

    /**
     * Handle save status check request
     */
    public static function handle_status_check() {
        // Verify security
        if (!self::verify_request()) {
            return;
        }

        $module = sanitize_text_field($_POST['module'] ?? '');
        
        if (empty($module)) {
            wp_send_json_error(array(
                'message' => __('Module parameter required.', 'redco-optimizer'),
                'code' => 'MISSING_MODULE'
            ));
            return;
        }

        try {
            // Get current module settings to verify they exist
            $settings = self::get_module_settings($module);
            
            wp_send_json_success(array(
                'module' => $module,
                'settings_count' => count($settings),
                'last_modified' => get_option('redco_optimizer_' . str_replace('-', '_', $module) . '_last_modified', ''),
                'memory_usage' => self::get_memory_usage_info(),
                'timestamp' => current_time('mysql')
            ));
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => __('Failed to check status.', 'redco-optimizer'),
                'code' => 'STATUS_CHECK_FAILED',
                'debug' => array(
                    'error' => $e->getMessage()
                )
            ));
        }
    }

    /**
     * Verify AJAX request security
     */
    private static function verify_request() {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_global_auto_save_nonce')) {
            wp_send_json_error(array(
                'message' => __('Security verification failed.', 'redco-optimizer'),
                'code' => 'NONCE_FAILED'
            ));
            return false;
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array(
                'message' => __('Insufficient permissions.', 'redco-optimizer'),
                'code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return false;
        }

        return true;
    }

    /**
     * Check memory usage before processing with intelligent thresholds (internal method)
     */
    private static function check_memory_usage_internal($module, $field_name) {
        // Temporary diagnostic logging
        $is_memory_intensive = self::is_truly_memory_intensive($module, $field_name);
        $field_key = self::extract_field_key($field_name);
        error_log("Redco Auto-Save Debug: Module={$module}, Field={$field_name}, FieldKey={$field_key}, MemoryIntensive={$is_memory_intensive}");

        // Force direct processing if enabled (bypass memory checks for most operations)
        if (self::$config['force_direct_processing'] && !$is_memory_intensive) {
            error_log("Redco Auto-Save Debug: Force direct processing - bypassing memory check");
            return true;
        }

        $memory_info = self::get_memory_usage_info();
        $threshold = self::get_memory_threshold($module, $field_name);

        error_log("Redco Auto-Save Debug: Memory={$memory_info['usage_percentage']}%, Threshold={$threshold}%");

        if ($memory_info['usage_percentage'] > $threshold) {
            error_log("Redco Auto-Save Debug: Memory threshold exceeded - checking if should queue");
            // Check if this is a truly memory-intensive operation
            $is_memory_intensive = self::is_truly_memory_intensive($module, $field_name);

            // Only queue truly memory-intensive operations if queue_only_critical is enabled
            if (self::$config['queue_enabled'] && $is_memory_intensive) {
                // Queue the save instead of failing
                $queue_data = array(
                    'module' => $module,
                    'field_name' => $field_name,
                    'field_value' => $_POST['field_value'] ?? ''
                );
                $queued = self::queue_auto_save($queue_data);
                if ($queued) {
                    wp_send_json_success(array(
                        'message' => __('Save queued due to high memory usage. Will process shortly.', 'redco-optimizer'),
                        'code' => 'QUEUED_FOR_PROCESSING',
                        'memory_info' => $memory_info,
                        'queued' => true
                    ));
                    return true;
                }
            }

            // For non-memory-intensive operations OR if memory isn't critically high, process directly
            if (!$is_memory_intensive || $memory_info['usage_percentage'] < 98) {
                return true;
            }

            // Provide more helpful error message for truly critical memory situations
            $message = $is_memory_intensive
                ? __('This setting requires more memory to process. Please try again in a moment, or contact your hosting provider to increase the memory limit.', 'redco-optimizer')
                : __('Memory usage critically high. Please try again in a moment.', 'redco-optimizer');

            wp_send_json_error(array(
                'message' => $message,
                'code' => 'MEMORY_LIMIT_EXCEEDED',
                'memory_info' => $memory_info,
                'threshold_used' => $threshold,
                'is_memory_intensive' => $is_memory_intensive,
                'suggestions' => self::get_memory_optimization_suggestions($module, $field_name)
            ));
            return false;
        }

        return true;
    }

    /**
     * Get memory usage information
     */
    private static function get_memory_usage_info() {
        $memory_usage = memory_get_usage(true);
        $memory_peak = memory_get_peak_usage(true);
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));

        return array(
            'current' => $memory_usage,
            'peak' => $memory_peak,
            'limit' => $memory_limit,
            'usage_percentage' => ($memory_usage / $memory_limit) * 100,
            'available' => $memory_limit - $memory_usage
        );
    }

    /**
     * Sanitize field value based on context
     */
    private static function sanitize_field_value($value, $field_name, $module) {
        // Handle different value types
        if (is_array($value)) {
            return array_map('sanitize_text_field', $value);
        }

        // Handle boolean-like values
        if (in_array($value, array('true', 'false', '1', '0'), true)) {
            return $value === 'true' || $value === '1' ? 1 : 0;
        }

        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? floatval($value) : intval($value);
        }

        // Handle URLs
        if (filter_var($value, FILTER_VALIDATE_URL)) {
            return esc_url_raw($value);
        }

        // Handle emails
        if (filter_var($value, FILTER_VALIDATE_EMAIL)) {
            return sanitize_email($value);
        }

        // Default text sanitization
        return sanitize_text_field($value);
    }

    /**
     * Extract field key from form field name (handles settings[field_name] format)
     */
    private static function extract_field_key($field_name) {
        // Handle settings[field_name] format
        if (preg_match('/^settings\[([^\]]+)\]$/', $field_name, $matches)) {
            return $matches[1];
        }

        // Handle other bracket formats
        if (preg_match('/^[^[]+\[([^\]]+)\]$/', $field_name, $matches)) {
            return $matches[1];
        }

        return $field_name;
    }

    /**
     * Get module settings with fallback to defaults
     */
    private static function get_module_settings($module) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Get defaults if available
        $defaults = array();
        if (class_exists('Redco_Config')) {
            $defaults = Redco_Config::get_module_defaults($module);
        }

        return get_option($option_name, $defaults);
    }

    /**
     * Save module settings with error handling
     */
    private static function save_module_settings($module, $settings) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Update last modified timestamp
        update_option($option_name . '_last_modified', current_time('mysql'));

        return update_option($option_name, $settings);
    }

    /**
     * Clear module cache efficiently
     */
    private static function clear_module_cache($module) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Clear WordPress object cache for this specific option only
        wp_cache_delete($option_name, 'options');

        // Clear module-specific transients only
        delete_transient('redco_' . $module . '_cache');
        delete_transient('redco_' . $module . '_settings');

        // Clear any module-specific caches
        if (function_exists('wp_cache_flush_group')) {
            wp_cache_flush_group('redco_' . $module);
        }
    }

    /**
     * Clear module cache with memory optimization for specific fields
     */
    private static function clear_module_cache_optimized($module, $field_key) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Always clear the option cache
        wp_cache_delete($option_name, 'options');

        // For memory-intensive modules, be more selective about cache clearing
        if ($module === 'asset-optimization') {
            // Only clear specific caches for asset optimization to reduce memory usage
            if (in_array($field_key, array('minify_css', 'minify_js', 'critical_css'))) {
                // Clear only optimization-related transients
                delete_transient('redco_' . $module . '_optimization_cache');

                // Skip heavy cache operations that might trigger memory issues
                return;
            }
        }

        // For other modules or non-memory-intensive fields, use standard cache clearing
        self::clear_module_cache($module);
    }

    /**
     * Get auto-save configuration
     */
    public static function get_config() {
        return self::$config;
    }

    /**
     * Update auto-save configuration
     */
    public static function update_config($key, $value) {
        if (isset(self::$config[$key])) {
            self::$config[$key] = $value;
        }
    }

    /**
     * Get appropriate memory threshold based on module and field
     */
    private static function get_memory_threshold($module, $field_name) {
        // Use higher threshold for memory-intensive operations
        if (self::is_memory_intensive_operation($module, $field_name)) {
            return self::$config['memory_limit_threshold_high_memory'];
        }

        return self::$config['memory_limit_threshold'];
    }

    /**
     * Check if operation is memory-intensive (legacy method - kept for compatibility)
     */
    private static function is_memory_intensive_operation($module, $field_name) {
        return self::is_truly_memory_intensive($module, $field_name);
    }

    /**
     * Check if operation is truly memory-intensive (much more selective)
     */
    private static function is_truly_memory_intensive($module, $field_name) {
        $field_key = self::extract_field_key($field_name);

        // Only consider specific field operations as truly memory-intensive
        $truly_intensive_fields = array('minify_css', 'minify_js', 'critical_css');

        // Must be asset-optimization module AND a truly intensive field
        if ($module === 'asset-optimization' && in_array($field_key, $truly_intensive_fields)) {
            return true;
        }

        // Database cleanup operations with large datasets
        if ($module === 'database-cleanup' && in_array($field_key, array('cleanup_revisions', 'cleanup_spam', 'cleanup_transients'))) {
            return true;
        }

        // Large cache clearing operations
        if ($field_key === 'clear_all_cache' || $field_key === 'force_cache_rebuild') {
            return true;
        }

        return false;
    }

    /**
     * Queue auto-save for later processing
     */
    private static function queue_auto_save($request_data) {
        $queue = get_option('redco_auto_save_queue', array());

        $queue_item = array(
            'module' => sanitize_text_field($request_data['module'] ?? ''),
            'field_name' => sanitize_text_field($request_data['field_name'] ?? ''),
            'field_value' => $request_data['field_value'] ?? '',
            'timestamp' => current_time('mysql'),
            'attempts' => 0,
            'user_id' => get_current_user_id()
        );

        $queue[] = $queue_item;

        // Limit queue size to prevent memory issues
        if (count($queue) > 50) {
            $queue = array_slice($queue, -50);
        }

        $success = update_option('redco_auto_save_queue', $queue);

        // Schedule immediate queue processing (reduce delay from 30s to 5s)
        if ($success && !wp_next_scheduled('redco_process_auto_save_queue')) {
            wp_schedule_single_event(time() + 5, 'redco_process_auto_save_queue');
        }

        // Also try to process immediately if WordPress cron is disabled
        if (defined('DISABLE_WP_CRON') && DISABLE_WP_CRON) {
            // Process queue immediately in background
            wp_remote_post(admin_url('admin-ajax.php'), array(
                'timeout' => 0.01,
                'blocking' => false,
                'body' => array(
                    'action' => 'redco_process_auto_save_queue_immediate',
                    'nonce' => wp_create_nonce('redco_queue_process')
                )
            ));
        }

        return $success;
    }

    /**
     * Process queued auto-save requests
     */
    public static function process_auto_save_queue() {
        $queue = get_option('redco_auto_save_queue', array());

        if (empty($queue)) {
            return;
        }

        $processed = array();
        $remaining = array();

        foreach ($queue as $item) {
            // Check memory before processing each item
            $memory_info = self::get_memory_usage_info();
            if ($memory_info['usage_percentage'] > 0.75) { // Use lower threshold for queue processing
                $remaining[] = $item;
                continue;
            }

            // Process the queued save
            $success = self::process_queued_save($item);

            if ($success) {
                $processed[] = $item;
            } else {
                $item['attempts']++;
                if ($item['attempts'] < 3) {
                    $remaining[] = $item;
                }
            }
        }

        // Update queue with remaining items
        update_option('redco_auto_save_queue', $remaining);

        // Schedule next processing if items remain
        if (!empty($remaining) && !wp_next_scheduled('redco_process_auto_save_queue')) {
            wp_schedule_single_event(time() + 60, 'redco_process_auto_save_queue');
        }

        return count($processed);
    }

    /**
     * Process a single queued save
     */
    private static function process_queued_save($item) {
        try {
            // Simulate the original request context
            $_POST['module'] = $item['module'];
            $_POST['field_name'] = $item['field_name'];
            $_POST['field_value'] = $item['field_value'];

            // Get current module settings
            $current_settings = self::get_module_settings($item['module']);

            // Sanitize and validate the field value
            $sanitized_value = self::sanitize_field_value($item['field_value'], $item['field_name'], $item['module']);

            // Preserve array structure
            $field_key = self::extract_field_key($item['field_name']);
            $current_settings[$field_key] = $sanitized_value;

            // Save the updated settings with minimal cache clearing
            $success = self::save_module_settings_minimal($item['module'], $current_settings);

            return $success;

        } catch (Exception $e) {
            error_log('Redco Auto-Save Queue Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Save module settings with minimal cache clearing for queued operations
     */
    private static function save_module_settings_minimal($module, $settings) {
        $option_name = 'redco_optimizer_' . str_replace('-', '_', $module);

        // Update last modified timestamp
        update_option($option_name . '_last_modified', current_time('mysql'));

        // Save settings
        $success = update_option($option_name, $settings);

        // Minimal cache clearing - only clear the specific option
        if ($success) {
            wp_cache_delete($option_name, 'options');
        }

        return $success;
    }

    /**
     * Get memory optimization suggestions
     */
    private static function get_memory_optimization_suggestions($module, $field_name) {
        $suggestions = array();

        if ($module === 'asset-optimization') {
            $suggestions[] = __('Consider disabling other optimization features temporarily while saving this setting.', 'redco-optimizer');
            $suggestions[] = __('Clear the asset optimization cache before making changes to reduce memory usage.', 'redco-optimizer');
        }

        if (in_array(self::extract_field_key($field_name), array('minify_css', 'minify_js'))) {
            $suggestions[] = __('This setting triggers file processing which requires additional memory.', 'redco-optimizer');
            $suggestions[] = __('Consider enabling this setting during off-peak hours.', 'redco-optimizer');
        }

        $suggestions[] = __('Contact your hosting provider to increase the PHP memory limit for better performance.', 'redco-optimizer');

        return $suggestions;
    }

    // Debug AJAX handlers removed for production

    /**
     * AJAX handler: Process queue immediately (for disabled cron environments)
     */
    public static function ajax_process_queue_immediate() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'redco_queue_process')) {
            wp_die('Security check failed');
        }

        // Process queue
        $processed_count = self::process_auto_save_queue();

        // Return minimal response to avoid blocking
        wp_die('OK');
    }

    /**
     * Add debugging method to check queue status
     */
    public static function get_queue_status() {
        $queue = get_option('redco_auto_save_queue', array());

        return array(
            'queue_size' => count($queue),
            'oldest_item' => !empty($queue) ? $queue[0]['timestamp'] : null,
            'newest_item' => !empty($queue) ? end($queue)['timestamp'] : null,
            'cron_scheduled' => wp_next_scheduled('redco_process_auto_save_queue'),
            'cron_disabled' => defined('DISABLE_WP_CRON') && DISABLE_WP_CRON
        );
    }

    /**
     * Force process queue (for debugging)
     */
    public static function force_process_queue() {
        return self::process_auto_save_queue();
    }

    // Debug queue management methods removed for production
}

// Initialize the global auto-save handler
Redco_Global_Auto_Save_Handler::init();
