/**
 * Loading Indicator Test
 * 
 * Simple test to verify the loading indicator is working
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Wait for everything to load
        setTimeout(function() {
            console.log('🧪 Testing loading indicator...');
            
            // Check if RedcoAutoSave exists
            if (typeof RedcoAutoSave === 'undefined') {
                console.error('❌ RedcoAutoSave not found');
                return;
            }
            
            console.log('✅ RedcoAutoSave found');
            
            // Initialize if not already done
            if (typeof RedcoAutoSave.init === 'function') {
                RedcoAutoSave.init();
                console.log('✅ RedcoAutoSave initialized');
            }
            
            // Check if loading indicator was created
            const $indicator = $('#redco-auto-save-loading');
            if ($indicator.length === 0) {
                console.error('❌ Loading indicator not found in DOM');
                return;
            }
            
            console.log('✅ Loading indicator found in DOM');
            
            // Test showing the indicator
            if (typeof RedcoAutoSave.showLoadingIndicator === 'function') {
                console.log('🧪 Testing show loading indicator...');
                RedcoAutoSave.showLoadingIndicator('test-module');
                
                setTimeout(() => {
                    console.log('🧪 Testing hide loading indicator...');
                    RedcoAutoSave.hideLoadingIndicator('success', 'Test completed!');
                }, 2000);
            } else {
                console.error('❌ showLoadingIndicator method not found');
            }
            
            // Add a test button to manually trigger the indicator
            const testButton = `
                <div style="position: fixed; bottom: 20px; left: 20px; z-index: 999999;">
                    <button id="test-loading-btn" style="background: #0073aa; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer;">
                        Test Loading Indicator
                    </button>
                </div>
            `;
            
            $('body').append(testButton);
            
            $('#test-loading-btn').on('click', function() {
                console.log('🧪 Manual test triggered');
                if (typeof RedcoAutoSave.showLoadingIndicator === 'function') {
                    RedcoAutoSave.showLoadingIndicator('manual-test');
                    setTimeout(() => {
                        RedcoAutoSave.hideLoadingIndicator('success', 'Manual test completed!');
                    }, 3000);
                }
            });
            
        }, 3000);
    });

})(jQuery);
