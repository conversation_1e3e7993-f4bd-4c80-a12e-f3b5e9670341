/**
 * CRITICAL FIX: Auto-Save Module
 * Extracted from large admin-scripts.js for better maintainability
 * Handles all auto-save functionality for module settings
 */

(function($) {
    'use strict';

    /**
     * Auto-Save Module
     */
    window.RedcoAutoSave = {
        
        /**
         * Configuration
         */
        config: {
            autoSaveDelay: 3000,
            maxAutoSaveRequests: 3,
            debounceDelay: 500
        },
        
        /**
         * State management
         */
        state: {
            timers: new Map(),
            inProgress: new Map(),
            lastSavedData: new Map(),
            requestCount: 0
        },
        
        /**
         * Initialize auto-save functionality
         */
        init: function() {
            this.bindEvents();
            this.setupModuleSaveSupport();
            this.createLoadingIndicator();
        },

        /**
         * Bind auto-save events
         */
        bindEvents: function() {
            const self = this;

            // General form input changes
            $(document).on('input change', '.redco-module-form input, .redco-module-form select, .redco-module-form textarea', function() {
                const $input = $(this);
                const $form = $input.closest('.redco-module-form');
                const module = $form.data('module');

                if (module) {
                    self.scheduleAutoSave($form, module);
                }
            });

            // Specific checkbox handlers
            $(document).on('change', '.redco-module-form input[type="checkbox"]', function() {
                const $checkbox = $(this);
                const $form = $checkbox.closest('.redco-module-form');
                const module = $form.data('module');

                if (module) {
                    self.scheduleAutoSave($form, module);
                }
            });

            // Range slider handlers
            $(document).on('input change mousemove', '.redco-module-form input[type="range"]', function() {
                const $slider = $(this);
                const $form = $slider.closest('.redco-module-form');
                const module = $form.data('module');

                if (module) {
                    self.updateSliderDisplay($slider);
                    self.scheduleAutoSave($form, module);
                }
            });
        },
        
        /**
         * Schedule auto-save for a module
         */
        scheduleAutoSave: function($form, module) {
            // Skip if auto-save is already in progress
            if (this.state.inProgress.get(module)) {
                return;
            }

            // Clear existing timer
            if (this.state.timers.has(module)) {
                clearTimeout(this.state.timers.get(module));
            }

            // Set new timer
            const timer = setTimeout(() => {
                this.performAutoSave($form, module);
            }, this.config.autoSaveDelay);

            this.state.timers.set(module, timer);
        },
        
        /**
         * Update slider display values
         */
        updateSliderDisplay: function($slider) {
            const sliderId = $slider.attr('id');
            if (sliderId) {
                // Update quality display for WebP module
                if (sliderId === 'quality') {
                    $('#quality-value').text($slider.val());
                }
                // Add other slider displays as needed
            }
        },
        
        /**
         * Perform auto-save for a specific module
         */
        performAutoSave: function($form, module) {
            // Check request limit
            if (this.state.requestCount >= this.config.maxAutoSaveRequests) {
                if (typeof redcoDebug !== 'undefined' && redcoDebug.enabled) {
                    redcoDebug.warn('Auto-save request limit reached, skipping save for', module);
                }
                return;
            }

            // Mark as in progress
            this.state.inProgress.set(module, true);
            this.state.requestCount++;

            // Show loading indicator
            this.showLoadingIndicator(module, $form);

            // Get form data
            const settings = this.getFormData($form);

            // Check if data has changed
            const currentDataString = JSON.stringify(settings);
            const lastDataString = this.state.lastSavedData.get(module);

            if (currentDataString === lastDataString) {
                this.state.inProgress.set(module, false);
                this.state.requestCount = Math.max(0, this.state.requestCount - 1);
                this.hideLoadingIndicator();
                return;
            }

            // Perform save
            this.saveSettings(module, settings, currentDataString);
        },
        
        /**
         * Get form data with proper checkbox handling and field name normalization
         */
        getFormData: function($form) {
            const formData = $form.serializeArray();
            const settings = {};

            // Convert form data to object with proper field name handling
            $.each(formData, function(i, field) {
                let fieldName = field.name;
                let fieldValue = field.value;

                // CRITICAL FIX: Handle settings[field_name] format
                if (fieldName.startsWith('settings[') && fieldName.endsWith(']')) {
                    fieldName = fieldName.replace('settings[', '').replace(']', '');
                }

                // Handle array fields (field_name[])
                if (fieldName.endsWith('[]')) {
                    const baseName = fieldName.slice(0, -2);
                    if (!settings[baseName]) {
                        settings[baseName] = [];
                    }
                    settings[baseName].push(fieldValue);
                } else {
                    // Handle multiple values for same field
                    if (settings[fieldName]) {
                        if (!Array.isArray(settings[fieldName])) {
                            settings[fieldName] = [settings[fieldName]];
                        }
                        settings[fieldName].push(fieldValue);
                    } else {
                        settings[fieldName] = fieldValue;
                    }
                }
            });

            // Handle unchecked checkboxes with proper field name normalization
            $form.find('input[type="checkbox"]').each(function() {
                const $checkbox = $(this);
                let name = $checkbox.attr('name');

                if (!name) return;

                // CRITICAL FIX: Handle settings[field_name] format for checkboxes
                if (name.startsWith('settings[') && name.endsWith(']')) {
                    name = name.replace('settings[', '').replace(']', '');
                }

                if (!$checkbox.is(':checked')) {
                    if (name.endsWith('[]')) {
                        const baseName = name.slice(0, -2);
                        if (!settings[baseName]) {
                            settings[baseName] = [];
                        }
                    } else {
                        if (!settings.hasOwnProperty(name)) {
                            settings[name] = false;
                        }
                    }
                } else {
                    if (!name.endsWith('[]')) {
                        settings[name] = true;
                    }
                }
            });

            return settings;
        },
        
        /**
         * Save settings via AJAX
         */
        saveSettings: function(module, settings, currentDataString) {
            const self = this;
            
            // CRITICAL FIX: Use centralized AJAX utility if available
            if (typeof RedcoAjax !== 'undefined') {
                RedcoAjax.request({
                    action: 'redco_save_module_settings',
                    data: {
                        module: module,
                        settings: settings,
                        nonce: redcoAjax.nonce
                    },
                    success: function(response) {
                        self.handleSaveSuccess(module, currentDataString, response);
                    },
                    error: function(error) {
                        self.handleSaveError(module, error);
                    }
                });
            } else {
                // Fallback to traditional AJAX
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_save_module_settings',
                        module: module,
                        settings: settings,
                        nonce: redcoAjax.nonce
                    },
                    success: function(response) {
                        self.handleSaveSuccess(module, currentDataString, response);
                    },
                    error: function(xhr, status, error) {
                        self.handleSaveError(module, error, xhr);
                    },
                    complete: function() {
                        self.handleSaveComplete(module);
                    }
                });
            }
        },
        
        /**
         * Handle successful save
         */
        handleSaveSuccess: function(module, currentDataString, response) {
            if (response.success) {
                this.state.lastSavedData.set(module, currentDataString);

                // Hide loading indicator with success state
                this.hideLoadingIndicator('success', 'Saved!');

                if (typeof showToast === 'function') {
                    showToast(response.data.message || 'Settings saved successfully', 'success', 2000);
                }
            } else {
                // CRITICAL FIX: Enhanced error handling with debug info
                const errorMessage = response.data?.message || 'Error saving settings';
                const debugInfo = response.data?.debug || {};

                // Hide loading indicator with error state
                this.hideLoadingIndicator('error', 'Save failed');

                if (typeof showToast === 'function') {
                    showToast(errorMessage, 'error', 5000);
                }
            }

            this.handleSaveComplete(module);
        },
        
        /**
         * Handle save error
         */
        handleSaveError: function(module, error, xhr) {
            // CRITICAL FIX: Enhanced error message extraction
            let errorMessage = 'Network error occurred';
            let debugInfo = {};

            if (typeof error === 'string') {
                errorMessage = error;
            } else if (xhr && xhr.responseText) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.data && response.data.message) {
                        errorMessage = response.data.message;
                        debugInfo = response.data.debug || {};
                    }
                } catch (e) {
                    errorMessage = `Server error: ${xhr.status} ${xhr.statusText}`;
                    debugInfo.raw_response = xhr.responseText;
                }
            }

            // Hide loading indicator with error state
            this.hideLoadingIndicator('error', 'Error');

            if (typeof showToast === 'function') {
                showToast(errorMessage, 'error', 5000);
            }

            this.handleSaveComplete(module);
        },
        
        /**
         * Handle save completion
         */
        handleSaveComplete: function(module) {
            this.state.inProgress.set(module, false);
            this.state.requestCount = Math.max(0, this.state.requestCount - 1);

            // Ensure loading indicator is hidden (fallback)
            // Note: This is a fallback - the indicator should already be hidden by success/error handlers
            setTimeout(() => {
                const $indicator = $('#redco-auto-save-loading');
                if ($indicator.is(':visible') && !$indicator.hasClass('success') && !$indicator.hasClass('error')) {
                    this.hideLoadingIndicator();
                }
            }, 100);
        },
        
        /**
         * Get current saved data for a module
         */
        getSavedData: function(module) {
            return this.state.lastSavedData.get(module);
        },
        
        /**
         * Clear saved data for a module
         */
        clearSavedData: function(module) {
            this.state.lastSavedData.delete(module);
        },

        /**
         * Setup module-level save support for enhanced auto-save integration
         */
        setupModuleSaveSupport: function() {
            const self = this;

            // Add method for scheduling module saves (called by field mapper)
            this.scheduleModuleSave = function($form, module) {
                if (self.state.inProgress.get(module)) {
                    return; // Save already in progress
                }

                // Clear existing timer
                if (self.state.timers.has(module)) {
                    clearTimeout(self.state.timers.get(module));
                }

                // Schedule new save
                const timer = setTimeout(function() {
                    self.performModuleSave($form, module);
                }, self.config.autoSaveDelay);

                self.state.timers.set(module, timer);
            };
        },

        /**
         * Perform module-level save
         */
        performModuleSave: function($form, module) {
            if (this.state.inProgress.get(module)) {
                return; // Save already in progress
            }

            if (this.state.requestCount >= this.config.maxAutoSaveRequests) {
                return; // Too many concurrent requests
            }

            this.state.inProgress.set(module, true);
            this.state.requestCount++;

            // Show loading indicator
            this.showLoadingIndicator(module, $form);

            // Get form data
            const settings = this.getFormData($form);
            const currentDataString = JSON.stringify(settings);

            // Check if data has changed
            const lastDataString = this.state.lastSavedData.get(module);

            if (currentDataString === lastDataString) {
                this.state.inProgress.set(module, false);
                this.state.requestCount = Math.max(0, this.state.requestCount - 1);
                this.hideLoadingIndicator();
                return;
            }

            // Perform save
            this.saveSettings(module, settings, currentDataString);
        },

        /**
         * Show loading indicator
         */
        showLoadingIndicator: function(module, $form) {
            const $indicator = $('#redco-auto-save-loading');

            // Check if indicator exists
            if ($indicator.length === 0) {
                return;
            }

            const $loadingText = $indicator.find('.redco-loading-text');

            // Clear any existing timeout
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
            }

            // Update text with module name if available
            if (module) {
                const moduleName = module.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                $loadingText.text(`Saving ${moduleName}...`);
            } else {
                $loadingText.text('Saving...');
            }

            // Remove any state classes and force display
            $indicator.removeClass('hide success error');
            $indicator.css({
                'display': 'flex',
                'opacity': '1',
                'transform': 'translateY(0)',
                'pointer-events': 'auto'
            });
            $indicator.addClass('show');

            // Update ARIA label for accessibility
            $indicator.attr('aria-label', `Saving ${module || 'settings'} in progress`);

            // Set timeout to hide indicator if save takes too long (fallback)
            this.loadingTimeout = setTimeout(() => {
                this.hideLoadingIndicator();
            }, 10000); // 10 second timeout
        },

        /**
         * Hide loading indicator
         */
        hideLoadingIndicator: function(state, message) {
            const $indicator = $('#redco-auto-save-loading');
            const $loadingText = $indicator.find('.redco-loading-text');

            // Clear timeout
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
                this.loadingTimeout = null;
            }

            // Show success or error state briefly before hiding
            if (state === 'success') {
                $indicator.removeClass('show').addClass('success');
                $loadingText.text(message || 'Saved!');
                $indicator.attr('aria-label', 'Settings saved successfully');

                setTimeout(() => {
                    this.fadeOutIndicator($indicator);
                }, 1000);
            } else if (state === 'error') {
                $indicator.removeClass('show').addClass('error');
                $loadingText.text(message || 'Save failed');
                $indicator.attr('aria-label', 'Settings save failed');

                setTimeout(() => {
                    this.fadeOutIndicator($indicator);
                }, 2000);
            } else {
                // Normal hide without state
                this.fadeOutIndicator($indicator);
            }
        },

        /**
         * Fade out loading indicator
         */
        fadeOutIndicator: function($indicator) {
            $indicator.removeClass('show success error').addClass('hide');

            setTimeout(() => {
                $indicator.fadeOut(200, () => {
                    // Reset state after hiding
                    $indicator.removeClass('hide');
                    $indicator.find('.redco-loading-text').text('Saving...');
                    $indicator.attr('aria-label', 'Saving settings');
                });
            }, 100);
        },

        /**
         * Create loading indicator element
         */
        createLoadingIndicator: function() {
            // Create loading indicator HTML
            const loadingHtml = `
                <div id="redco-auto-save-loading" class="redco-loading-indicator" style="display: none;" aria-live="polite" aria-label="Saving settings">
                    <div class="redco-loading-spinner">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="8" cy="8" r="6" stroke="#0073aa" stroke-width="2" stroke-linecap="round" stroke-dasharray="9.42" stroke-dashoffset="9.42">
                                <animateTransform attributeName="transform" dur="1s" type="rotate" values="0 8 8;360 8 8" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="1s" values="9.42;0;9.42" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <span class="redco-loading-text">Saving...</span>
                </div>
            `;

            // Add to body if not already present
            if ($('#redco-auto-save-loading').length === 0) {
                $('body').append(loadingHtml);
                this.addLoadingIndicatorStyles();
            }
        },

        /**
         * Add CSS styles for loading indicator
         */
        addLoadingIndicatorStyles: function() {
            const styles = `
                <style id="redco-loading-styles">
                .redco-loading-indicator {
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    background: rgba(255, 255, 255, 0.95) !important;
                    border: 1px solid #ddd !important;
                    border-radius: 6px !important;
                    padding: 8px 12px !important;
                    display: none !important;
                    align-items: center !important;
                    gap: 8px !important;
                    font-size: 13px !important;
                    color: #333 !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                    z-index: 999999 !important;
                    backdrop-filter: blur(2px) !important;
                    transition: opacity 0.2s ease-in-out !important;
                }

                .redco-loading-indicator.show {
                    display: flex !important;
                    opacity: 1 !important;
                }

                .redco-loading-indicator.hide {
                    opacity: 0 !important;
                }

                .redco-loading-spinner {
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                }

                .redco-loading-spinner svg {
                    animation: redco-spin 1s linear infinite !important;
                }

                @keyframes redco-spin {
                    from { transform: rotate(0deg); }
                    to { transform: rotate(360deg); }
                }

                .redco-loading-text {
                    font-weight: 500 !important;
                    white-space: nowrap !important;
                }

                /* Success state */
                .redco-loading-indicator.success {
                    background: rgba(70, 180, 80, 0.95) !important;
                    color: white !important;
                    border-color: #46b450 !important;
                }

                /* Error state */
                .redco-loading-indicator.error {
                    background: rgba(220, 53, 69, 0.95) !important;
                    color: white !important;
                    border-color: #dc3545 !important;
                }
                </style>
            `;

            // Add styles if not already present
            if ($('#redco-loading-styles').length === 0) {
                $('head').append(styles);
            }
        },

        /**
         * Show loading indicator
         */
        showLoadingIndicator: function(module, $form) {
            const $indicator = $('#redco-auto-save-loading');

            // Check if indicator exists
            if ($indicator.length === 0) {
                return;
            }

            const $loadingText = $indicator.find('.redco-loading-text');

            // Clear any existing timeout
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
            }

            // Update text with module name if available
            if (module) {
                const moduleName = module.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                $loadingText.text(`Saving ${moduleName}...`);
            } else {
                $loadingText.text('Saving...');
            }

            // Remove any state classes and force display
            $indicator.removeClass('hide success error');
            $indicator.css({
                'display': 'flex',
                'opacity': '1',
                'transform': 'translateY(0)',
                'pointer-events': 'auto'
            });
            $indicator.addClass('show');

            // Update ARIA label for accessibility
            $indicator.attr('aria-label', `Saving ${module || 'settings'} in progress`);

            // Set timeout to hide indicator if save takes too long (fallback)
            this.loadingTimeout = setTimeout(() => {
                this.hideLoadingIndicator();
            }, 10000); // 10 second timeout
        },

        /**
         * Hide loading indicator
         */
        hideLoadingIndicator: function(state, message) {
            const $indicator = $('#redco-auto-save-loading');
            const $loadingText = $indicator.find('.redco-loading-text');

            // Clear timeout
            if (this.loadingTimeout) {
                clearTimeout(this.loadingTimeout);
                this.loadingTimeout = null;
            }

            // Show success or error state briefly before hiding
            if (state === 'success') {
                $indicator.removeClass('show').addClass('success');
                $loadingText.text(message || 'Saved!');
                $indicator.attr('aria-label', 'Settings saved successfully');

                setTimeout(() => {
                    this.fadeOutIndicator($indicator);
                }, 1000);
            } else if (state === 'error') {
                $indicator.removeClass('show').addClass('error');
                $loadingText.text(message || 'Save failed');
                $indicator.attr('aria-label', 'Settings save failed');

                setTimeout(() => {
                    this.fadeOutIndicator($indicator);
                }, 2000);
            } else {
                // Normal hide without state
                this.fadeOutIndicator($indicator);
            }
        },

        /**
         * Fade out loading indicator
         */
        fadeOutIndicator: function($indicator) {
            $indicator.removeClass('show success error').addClass('hide');

            setTimeout(() => {
                $indicator.fadeOut(200, () => {
                    // Reset state after hiding
                    $indicator.removeClass('hide');
                    $indicator.find('.redco-loading-text').text('Saving...');
                    $indicator.attr('aria-label', 'Saving settings');
                });
            }, 100);
        }
    };

    // Initialize auto-save when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer pages
        if ($('.redco-module-form, .redco-optimizer-admin').length > 0) {
            RedcoAutoSave.init();
        }
    });

})(jQuery);
