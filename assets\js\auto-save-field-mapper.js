/**
 * Auto-Save Field Mapper
 * 
 * Automatically detects and configures form fields for auto-save functionality
 * across all Redco Optimizer modules. Handles field mapping, attribute assignment,
 * and ensures proper integration between different auto-save systems.
 */

(function($) {
    'use strict';

    const AutoSaveFieldMapper = {
        
        /**
         * Initialize field mapping
         */
        init: function() {
            this.mapModuleFormFields();
            this.mapIndividualFields();
            this.setupFormValuePopulation();
            this.bindAutoSaveEvents();
        },

        /**
         * Map module form fields for module-level auto-save
         */
        mapModuleFormFields: function() {
            $('.redco-module-form').each(function() {
                const $form = $(this);
                const module = $form.data('module');
                
                if (!module) return;

                // Add auto-save class to form
                $form.addClass('auto-save-enabled');

                // Map all form fields
                $form.find('input, select, textarea').each(function() {
                    const $field = $(this);
                    const fieldName = $field.attr('name');
                    
                    if (!fieldName) return;

                    // Extract clean field name from settings[field_name] format
                    let cleanFieldName = fieldName;
                    if (fieldName.startsWith('settings[') && fieldName.endsWith(']')) {
                        cleanFieldName = fieldName.replace('settings[', '').replace(']', '');
                    }

                    // Add data attributes for enhanced auto-save
                    $field.attr('data-module', module);
                    $field.attr('data-setting', cleanFieldName);
                    $field.addClass('auto-save-field');

                    // Add visual indicator container
                    if ($field.closest('.setting-item, .form-group').find('.auto-save-status').length === 0) {
                        $field.after('<span class="auto-save-status"></span>');
                    }
                });
            });
        },

        /**
         * Map individual fields that may not be in module forms
         */
        mapIndividualFields: function() {
            // Look for fields with specific patterns
            $('input[name*="redco_"], select[name*="redco_"], textarea[name*="redco_"]').each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                
                if ($field.hasClass('auto-save-field')) return; // Already mapped

                // Extract module and setting from field name
                const nameMatch = fieldName.match(/redco_optimizer_([^_\[]+)(?:\[([^\]]+)\])?/);
                if (nameMatch) {
                    const module = nameMatch[1].replace('_', '-');
                    const setting = nameMatch[2] || fieldName.split('_').pop();

                    $field.attr('data-module', module);
                    $field.attr('data-setting', setting);
                    $field.addClass('auto-save-field');
                }
            });
        },

        /**
         * Setup form value population from saved settings
         */
        setupFormValuePopulation: function() {
            // Populate module form fields with saved values
            $('.redco-module-form').each(function() {
                const $form = $(this);
                const module = $form.data('module');
                
                if (!module) return;

                // Request current settings from server
                AutoSaveFieldMapper.loadModuleSettings(module, function(settings) {
                    AutoSaveFieldMapper.populateFormFields($form, settings);
                });
            });
        },

        /**
         * Load module settings from server with persistence verification
         */
        loadModuleSettings: function(module, callback) {
            // CRITICAL FIX: Add cache busting to ensure fresh data
            const cacheBuster = Date.now();

            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_module_settings',
                    module: module,
                    nonce: redcoAjax.nonce,
                    _cache_bust: cacheBuster
                },
                cache: false, // Disable jQuery caching
                success: function(response) {
                    if (response.success && callback) {
                        const settings = response.data.settings || {};



                        callback(settings);
                    } else {
                        if (callback) callback({});
                    }
                },
                error: function(xhr, status, error) {
                    // Silently fail - form will show default values
                    if (callback) callback({});
                }
            });
        },

        /**
         * Populate form fields with settings values and verify population
         */
        populateFormFields: function($form, settings) {
            let populatedCount = 0;
            let totalFields = 0;
            const populationLog = [];

            $form.find('input, select, textarea').each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                const settingName = $field.data('setting');

                totalFields++;

                if (!settingName) {
                    populationLog.push({
                        field: fieldName,
                        status: 'no_setting_name',
                        message: 'Field has no data-setting attribute'
                    });
                    return;
                }

                if (!settings.hasOwnProperty(settingName)) {
                    populationLog.push({
                        field: fieldName,
                        setting: settingName,
                        status: 'no_value',
                        message: 'No value found in settings'
                    });
                    return;
                }

                const value = settings[settingName];
                let populated = false;

                // Set field value based on type
                if ($field.is(':checkbox')) {
                    const shouldBeChecked = !!value;
                    $field.prop('checked', shouldBeChecked);
                    populated = $field.is(':checked') === shouldBeChecked;
                } else if ($field.is(':radio')) {
                    if ($field.val() === String(value)) {
                        $field.prop('checked', true);
                        populated = true;
                    }
                } else {
                    $field.val(value);
                    populated = $field.val() === String(value);
                }

                if (populated) {
                    populatedCount++;
                    populationLog.push({
                        field: fieldName,
                        setting: settingName,
                        value: value,
                        status: 'success',
                        message: 'Field populated successfully'
                    });
                } else {
                    populationLog.push({
                        field: fieldName,
                        setting: settingName,
                        expected: value,
                        actual: $field.val(),
                        status: 'failed',
                        message: 'Field population failed'
                    });
                }
            });


        },

        /**
         * Bind auto-save events to mapped fields
         */
        bindAutoSaveEvents: function() {
            // Enhanced auto-save for individual fields
            $(document).on('change input', '.auto-save-field[data-module][data-setting]', function() {
                const $field = $(this);
                const module = $field.data('module');
                const setting = $field.data('setting');
                
                if (window.RedcoAutoSave && typeof window.RedcoAutoSave.scheduleAutoSave === 'function') {
                    window.RedcoAutoSave.scheduleAutoSave($field, module, setting);
                }
            });

            // Module-level auto-save for forms
            $(document).on('change input', '.redco-module-form input, .redco-module-form select, .redco-module-form textarea', function() {
                const $field = $(this);
                const $form = $field.closest('.redco-module-form');
                const module = $form.data('module');
                
                if (module && window.RedcoAutoSave && typeof window.RedcoAutoSave.scheduleModuleSave === 'function') {
                    window.RedcoAutoSave.scheduleModuleSave($form, module);
                }
            });
        },

        /**
         * Get field mapping statistics for debugging
         */
        getStats: function() {
            return {
                totalFields: $('.auto-save-field').length,
                moduleFields: $('.auto-save-field[data-module]').length,
                enhancedFields: $('.auto-save-field[data-module][data-setting]').length,
                moduleForms: $('.redco-module-form').length,
                mappedForms: $('.redco-module-form[data-module]').length
            };
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        // Only initialize on Redco Optimizer pages
        if ($('.redco-optimizer-admin, .redco-module-tab, .redco-module-form').length > 0) {
            AutoSaveFieldMapper.init();
            
            // Debug output in console
            if (typeof redcoAjax !== 'undefined' && redcoAjax.debug) {
                console.log('Auto-Save Field Mapper initialized:', AutoSaveFieldMapper.getStats());
            }
        }
    });

    // Expose to global scope
    window.AutoSaveFieldMapper = AutoSaveFieldMapper;

})(jQuery);
