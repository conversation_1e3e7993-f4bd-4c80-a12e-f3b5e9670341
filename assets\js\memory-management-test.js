/**
 * Memory Management Test for Global Auto-Save System
 * 
 * Tests the improved memory handling and queue functionality
 */

(function($) {
    'use strict';

    window.RedcoMemoryManagementTest = {
        
        // Test configuration
        config: {
            testModule: 'asset-optimization',
            testField: 'settings[minify_css]',
            memoryTestIterations: 10
        },

        // Test results
        results: {
            memoryTests: [],
            queueTests: [],
            errorHandlingTests: [],
            errors: []
        },

        // Initialize memory management test
        init: function() {
            console.log('🧠 Initializing Memory Management Test...');
            this.createTestInterface();
            this.runMemoryTests();
        },

        // Create test interface
        createTestInterface: function() {
            if ($('#redco-memory-test-container').length === 0) {
                const testInterface = `
                    <div id="redco-memory-test-container" style="position: fixed; top: 50px; right: 20px; background: white; border: 2px solid #007cba; padding: 20px; z-index: 10000; width: 350px; max-height: 500px; overflow-y: auto;">
                        <h4>🧠 Memory Management Test</h4>
                        <div id="memory-test-status">Initializing...</div>
                        <div id="memory-test-results" style="margin-top: 15px; font-size: 12px; max-height: 300px; overflow-y: auto;"></div>
                        <div style="margin-top: 15px;">
                            <button type="button" id="run-memory-stress-test">Run Stress Test</button>
                            <button type="button" id="test-queue-functionality">Test Queue</button>
                            <button type="button" id="close-memory-test">Close</button>
                        </div>
                    </div>
                `;
                
                $('body').append(testInterface);
                
                // Bind event handlers
                $('#close-memory-test').on('click', function() {
                    $('#redco-memory-test-container').remove();
                });
                
                $('#run-memory-stress-test').on('click', () => {
                    this.runStressTest();
                });
                
                $('#test-queue-functionality').on('click', () => {
                    this.testQueueFunctionality();
                });
            }
        },

        // Run memory tests
        runMemoryTests: function() {
            this.updateStatus('Running memory management tests...');
            
            const tests = [
                this.testMemoryThresholds,
                this.testMemoryIntensiveDetection,
                this.testErrorHandling,
                this.testQueueBasics
            ];

            let testIndex = 0;
            const runNextTest = () => {
                if (testIndex < tests.length) {
                    const test = tests[testIndex];
                    console.log(`Running memory test: ${test.name}`);
                    
                    try {
                        const result = test.call(this);
                        if (result instanceof Promise) {
                            result.then(() => {
                                testIndex++;
                                setTimeout(runNextTest, 500);
                            }).catch((error) => {
                                this.recordError(test.name, error.message);
                                testIndex++;
                                setTimeout(runNextTest, 500);
                            });
                        } else {
                            testIndex++;
                            setTimeout(runNextTest, 500);
                        }
                    } catch (error) {
                        this.recordError(test.name, error.message);
                        testIndex++;
                        setTimeout(runNextTest, 500);
                    }
                } else {
                    this.displayResults();
                }
            };

            runNextTest();
        },

        // Test 1: Memory Thresholds
        testMemoryThresholds: function() {
            const testName = 'Memory Thresholds';
            
            try {
                // Check if global auto-save system is available
                if (typeof RedcoGlobalAutoSave === 'undefined') {
                    throw new Error('RedcoGlobalAutoSave not available');
                }

                // Test memory threshold configuration
                const config = RedcoGlobalAutoSave.config;
                if (!config.memoryThreshold) {
                    throw new Error('Memory threshold not configured');
                }

                this.recordTestResult(testName, true, `Memory threshold: ${config.memoryThreshold * 100}%`);
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 2: Memory Intensive Detection
        testMemoryIntensiveDetection: function() {
            const testName = 'Memory Intensive Detection';
            
            try {
                // Test if asset-optimization module is detected as memory intensive
                const testField = $('#test-memory-field');
                if (testField.length === 0) {
                    // Create a test field
                    $('body').append('<input type="hidden" id="test-memory-field" name="settings[minify_css]" value="1">');
                }

                this.recordTestResult(testName, true, 'Memory intensive detection configured');
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 3: Error Handling
        testErrorHandling: function() {
            const testName = 'Error Handling';
            
            return new Promise((resolve, reject) => {
                try {
                    // Mock a memory limit exceeded error
                    const mockErrorData = {
                        code: 'MEMORY_LIMIT_EXCEEDED',
                        message: 'Memory usage too high',
                        memory_info: {
                            usage_percentage: 90,
                            current: 1000000,
                            limit: 1200000
                        },
                        suggestions: ['Test suggestion']
                    };

                    // Test if error handling methods exist
                    if (typeof RedcoGlobalAutoSave.handleSaveError === 'function') {
                        this.recordTestResult(testName, true, 'Error handling methods available');
                    } else {
                        this.recordTestResult(testName, false, 'Error handling methods missing');
                    }
                    
                    resolve();
                } catch (error) {
                    this.recordTestResult(testName, false, error.message);
                    reject(error);
                }
            });
        },

        // Test 4: Queue Basics
        testQueueBasics: function() {
            const testName = 'Queue Basics';
            
            try {
                // Test if queue-related methods are available
                const hasQueueMethods = typeof RedcoGlobalAutoSave.checkQueuedSaveStatus === 'function' &&
                                       typeof RedcoGlobalAutoSave.handleMemoryLimitError === 'function';

                if (hasQueueMethods) {
                    this.recordTestResult(testName, true, 'Queue methods available');
                } else {
                    this.recordTestResult(testName, false, 'Queue methods missing');
                }
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Run stress test
        runStressTest: function() {
            this.updateStatus('Running stress test...');
            console.log('🔥 Starting memory stress test...');
            
            let iteration = 0;
            const maxIterations = this.config.memoryTestIterations;
            
            const runIteration = () => {
                if (iteration < maxIterations) {
                    iteration++;
                    
                    // Simulate rapid auto-save requests
                    this.simulateAutoSaveRequest(this.config.testModule, this.config.testField, `test-value-${iteration}`)
                        .then(() => {
                            this.updateResults(`Stress test iteration ${iteration}/${maxIterations} completed`);
                            setTimeout(runIteration, 100);
                        })
                        .catch((error) => {
                            this.recordError('Stress Test', `Iteration ${iteration}: ${error.message}`);
                            setTimeout(runIteration, 100);
                        });
                } else {
                    this.updateStatus('Stress test completed');
                    console.log('🎯 Stress test completed');
                }
            };
            
            runIteration();
        },

        // Test queue functionality
        testQueueFunctionality: function() {
            this.updateStatus('Testing queue functionality...');
            console.log('📋 Testing queue functionality...');
            
            // Simulate a memory-constrained save that should be queued
            this.simulateMemoryConstrainedSave()
                .then(() => {
                    this.updateResults('Queue functionality test completed');
                })
                .catch((error) => {
                    this.recordError('Queue Test', error.message);
                });
        },

        // Simulate auto-save request
        simulateAutoSaveRequest: function(module, fieldName, value) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_global_auto_save',
                        nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                        module: module,
                        field_name: fieldName,
                        field_value: value
                    },
                    timeout: 10000,
                    success: function(response) {
                        if (response.success) {
                            resolve(response.data);
                        } else {
                            reject(new Error(response.data.message || 'Save failed'));
                        }
                    },
                    error: function(xhr, status, error) {
                        reject(new Error(`Network error: ${error}`));
                    }
                });
            });
        },

        // Simulate memory-constrained save
        simulateMemoryConstrainedSave: function() {
            return this.simulateAutoSaveRequest(this.config.testModule, this.config.testField, 'memory-test-value');
        },

        // Record test result
        recordTestResult: function(testName, passed, message) {
            this.results.memoryTests.push({
                name: testName,
                passed: passed,
                message: message
            });

            const icon = passed ? '✅' : '❌';
            console.log(`${icon} ${testName}: ${message}`);
        },

        // Record error
        recordError: function(testName, message) {
            this.results.errors.push({
                test: testName,
                message: message
            });
            console.error(`❌ ${testName}: ${message}`);
        },

        // Update status
        updateStatus: function(status) {
            $('#memory-test-status').text(status);
        },

        // Update results
        updateResults: function(result) {
            const $results = $('#memory-test-results');
            $results.append(`<div>${result}</div>`);
            $results.scrollTop($results[0].scrollHeight);
        },

        // Display final results
        displayResults: function() {
            const totalTests = this.results.memoryTests.length;
            const passedTests = this.results.memoryTests.filter(t => t.passed).length;
            const errorCount = this.results.errors.length;
            
            this.updateStatus(`Tests completed: ${passedTests}/${totalTests} passed, ${errorCount} errors`);
            
            console.log('\n🧠 Memory Management Test Results:');
            console.log(`Total Tests: ${totalTests}`);
            console.log(`Passed: ${passedTests}`);
            console.log(`Failed: ${totalTests - passedTests}`);
            console.log(`Errors: ${errorCount}`);
            
            // Display individual results
            this.results.memoryTests.forEach(test => {
                const icon = test.passed ? '✅' : '❌';
                this.updateResults(`${icon} ${test.name}: ${test.message}`);
            });
            
            if (this.results.errors.length > 0) {
                this.updateResults('<strong>Errors:</strong>');
                this.results.errors.forEach(error => {
                    this.updateResults(`❌ ${error.test}: ${error.message}`);
                });
            }
        }
    };

    // Auto-run test in debug mode
    $(document).ready(function() {
        if (window.location.href.includes('debug=1') || window.location.href.includes('test=memory')) {
            setTimeout(() => {
                RedcoMemoryManagementTest.init();
            }, 3000);
        }
    });

    // Add console helper
    window.testMemoryManagement = function() {
        RedcoMemoryManagementTest.init();
    };

})(jQuery);

console.log('🧠 Memory Management Test loaded');
console.log('💡 Use testMemoryManagement() to run memory management tests');
