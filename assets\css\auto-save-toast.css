/**
 * Toast Notification System for Auto-Save
 * Provides centralized user feedback for auto-save operations
 */

/* Toast Container */
.redco-toast-container {
    position: fixed;
    top: 32px; /* Below WordPress admin bar */
    right: 20px;
    z-index: 999999;
    pointer-events: none;
    max-width: 400px;
}

/* Individual Toast */
.redco-toast {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: auto;
    border-left: 4px solid #007cba;
    max-width: 100%;
    word-wrap: break-word;
}

.redco-toast.show {
    opacity: 1;
    transform: translateX(0);
}

/* Toast Content */
.toast-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 10px;
}

/* Toast Icon */
.toast-icon {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
    position: relative;
}

.toast-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

/* Toast Message */
.toast-message {
    flex: 1;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    font-weight: 500;
}

/* Toast Close Button */
.toast-close {
    background: none;
    border: none;
    font-size: 18px;
    line-height: 1;
    color: #666;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.toast-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #333;
}

.toast-close:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* Toast Types */

/* Info Toast (Default - Saving) */
.redco-toast-info {
    border-left-color: #007cba;
}

.redco-toast-info .toast-icon {
    background: #e7f3ff;
}

.redco-toast-info .toast-icon::before {
    background: #007cba;
}

/* Success Toast (Saved) */
.redco-toast-success {
    border-left-color: #28a745;
}

.redco-toast-success .toast-icon {
    background: #d4edda;
}

.redco-toast-success .toast-icon::before {
    background: #28a745;
}

/* Warning Toast (Queued) */
.redco-toast-warning {
    border-left-color: #ffc107;
}

.redco-toast-warning .toast-icon {
    background: #fff3cd;
}

.redco-toast-warning .toast-icon::before {
    background: #ffc107;
}

/* Error Toast (Failed) */
.redco-toast-error {
    border-left-color: #dc3545;
}

.redco-toast-error .toast-icon {
    background: #f8d7da;
}

.redco-toast-error .toast-icon::before {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .redco-toast-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .redco-toast {
        margin-bottom: 8px;
    }
    
    .toast-content {
        padding: 10px 12px;
        gap: 8px;
    }
    
    .toast-message {
        font-size: 13px;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .redco-toast {
        border: 2px solid #000;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    
    .toast-message {
        color: #000;
        font-weight: 600;
    }
    
    .toast-close {
        color: #000;
        border: 1px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .redco-toast {
        transition: opacity 0.2s ease;
        transform: none;
    }
    
    .redco-toast.show {
        transform: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .redco-toast {
        background: #2c3e50;
        color: #ecf0f1;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .toast-message {
        color: #ecf0f1;
    }
    
    .toast-close {
        color: #bdc3c7;
    }
    
    .toast-close:hover {
        background: rgba(255, 255, 255, 0.1);
        color: #ecf0f1;
    }
    
    .redco-toast-info .toast-icon {
        background: rgba(0, 124, 186, 0.2);
    }
    
    .redco-toast-success .toast-icon {
        background: rgba(40, 167, 69, 0.2);
    }
    
    .redco-toast-warning .toast-icon {
        background: rgba(255, 193, 7, 0.2);
    }
    
    .redco-toast-error .toast-icon {
        background: rgba(220, 53, 69, 0.2);
    }
}

/* Animation for Loading State */
.redco-toast-info .toast-icon::before {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
    50% {
        opacity: 0.7;
        transform: translate(-50%, -50%) scale(1.1);
    }
}

/* Success Checkmark Animation */
.redco-toast-success .toast-icon::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #28a745;
    font-size: 12px;
    font-weight: bold;
    animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

/* Warning Icon */
.redco-toast-warning .toast-icon::after {
    content: '!';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #ffc107;
    font-size: 12px;
    font-weight: bold;
}

/* Error Icon */
.redco-toast-error .toast-icon::after {
    content: '✕';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #dc3545;
    font-size: 10px;
    font-weight: bold;
}

/* Accessibility Improvements */
.redco-toast[role="alert"] {
    /* Ensure screen readers announce immediately */
}

.toast-close[aria-label] {
    /* Ensure close button is properly labeled */
}

/* Focus Management */
.redco-toast:focus-within {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}
