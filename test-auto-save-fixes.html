<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Save Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-form {
            margin: 20px 0;
        }
        
        .form-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            min-width: 200px;
            font-weight: bold;
        }
        
        input, select, textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status-check {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffa500; }
        .info { color: #007cba; }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        /* Include auto-save indicator styles */
        .redco-auto-save-status {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 12px;
            line-height: 1;
            vertical-align: middle;
        }
        
        .redco-auto-save-status .status-icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
            display: inline-block;
            border-radius: 50%;
            position: relative;
        }
        
        .redco-auto-save-status .status-text {
            color: #666;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .redco-auto-save-status.saving .status-icon {
            background-color: #ffa500;
            border: 2px solid #ff8c00;
        }
        
        .redco-auto-save-status.saving .status-text {
            color: #ff8c00;
        }
        
        .redco-auto-save-status.saved .status-icon {
            background-color: #28a745;
            border: 2px solid #1e7e34;
        }
        
        .redco-auto-save-status.saved .status-text {
            color: #1e7e34;
        }
        
        .redco-auto-save-status.error .status-icon {
            background-color: #dc3545;
            border: 2px solid #c82333;
        }
        
        .redco-auto-save-status.error .status-text {
            color: #c82333;
        }
        
        .redco-auto-save-status.queued .status-icon {
            background-color: #17a2b8;
            border: 2px solid #138496;
        }
        
        .redco-auto-save-status.queued .status-text {
            color: #138496;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #005a87;
        }

        /* Custom field type styles */
        .redco-toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .redco-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .redco-toggle-switch input:checked + .toggle-slider {
            background-color: #2196F3;
        }

        .redco-toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .threshold-slider-container, .quality-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .threshold-slider, .enhanced-slider, .quality-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
        }

        .threshold-input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .threshold-unit {
            font-size: 12px;
            color: #666;
        }

        .quality-display {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            color: #007cba;
        }

        .redco-select, .redco-text-input, .redco-number-input {
            border: 2px solid #007cba;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Auto-Save Fixes Test</h1>
        <p>Testing the fixes for memory threshold calculation and status indicators.</p>
        
        <div class="status-check">
            <h3>✅ Fixes Applied</h3>
            <ul>
                <li class="success">✅ <strong>Memory Threshold Bug Fixed:</strong> Thresholds now correctly return percentages (95%, 98%) instead of decimals (0.95%, 0.98%)</li>
                <li class="success">✅ <strong>Architecture Fixed:</strong> Memory check integrated into save handler instead of separate hook</li>
                <li class="info">🔍 <strong>Status Indicators:</strong> Testing if CSS and JavaScript are working correctly</li>
            </ul>
        </div>
        
        <div class="test-container">
            <h3>🧪 Asset Optimization Test Form</h3>
            <form class="redco-module-form test-form" data-module="asset-optimization">

                <div class="form-group">
                    <label for="test_enabled">Enable Asset Optimization:</label>
                    <input type="checkbox" id="test_enabled" name="settings[enabled]">
                    <span class="info">(Should save directly - NOT queued)</span>
                </div>

                <div class="form-group">
                    <label for="test_lazy_load">Enable Lazy Loading:</label>
                    <input type="checkbox" id="test_lazy_load" name="settings[lazy_load]">
                    <span class="info">(Should save directly - NOT queued)</span>
                </div>

                <div class="form-group">
                    <label for="test_minify_css">Enable CSS Minification:</label>
                    <input type="checkbox" id="test_minify_css" name="settings[minify_css]">
                    <span class="warning">(May be queued if memory > 95%)</span>
                </div>

                <div class="form-group">
                    <label for="test_minify_js">Enable JS Minification:</label>
                    <input type="checkbox" id="test_minify_js" name="settings[minify_js]">
                    <span class="warning">(May be queued if memory > 95%)</span>
                </div>

                <div class="form-group">
                    <label for="test_critical_css">Enable Critical CSS:</label>
                    <input type="checkbox" id="test_critical_css" name="settings[critical_css]">
                    <span class="warning">(May be queued if memory > 98%)</span>
                </div>

            </form>
        </div>

        <div class="test-container">
            <h3>🎛️ Custom Field Types Test</h3>
            <form class="redco-module-form test-form" data-module="lazy-load">

                <div class="form-group">
                    <label for="toggle_test">Toggle Switch Test:</label>
                    <div class="redco-toggle-switch">
                        <input type="checkbox" id="toggle_test" name="settings[toggle_enabled]">
                        <span class="toggle-slider"></span>
                    </div>
                    <span class="info">(Custom toggle switch)</span>
                </div>

                <div class="form-group">
                    <label for="range_test">Range Slider Test:</label>
                    <div class="threshold-slider-container">
                        <input type="range" id="range_test" name="settings[threshold]" min="0" max="1000" value="200" class="threshold-slider">
                        <input type="number" name="settings[threshold_number]" value="200" class="threshold-input">
                        <span class="threshold-unit">px</span>
                    </div>
                    <span class="info">(Range slider with number input)</span>
                </div>

                <div class="form-group">
                    <label for="quality_test">Quality Slider Test:</label>
                    <div class="quality-slider-container">
                        <input type="range" id="quality_test" name="settings[quality]" min="1" max="100" value="85" class="enhanced-slider">
                        <div class="quality-display">
                            <span id="quality-value">85</span>%
                        </div>
                    </div>
                    <span class="info">(Quality slider)</span>
                </div>

                <div class="form-group">
                    <label for="custom_select">Custom Select:</label>
                    <select id="custom_select" name="settings[custom_option]" class="redco-select">
                        <option value="option1">Option 1</option>
                        <option value="option2">Option 2</option>
                        <option value="option3">Option 3</option>
                    </select>
                    <span class="info">(Custom select dropdown)</span>
                </div>

                <div class="form-group">
                    <label for="custom_text">Custom Text Input:</label>
                    <input type="text" id="custom_text" name="settings[custom_text]" class="redco-text-input" placeholder="Enter text...">
                    <span class="info">(Custom text input)</span>
                </div>

                <div class="form-group">
                    <label for="custom_number">Custom Number Input:</label>
                    <input type="number" id="custom_number" name="settings[custom_number]" class="redco-number-input" min="0" max="100" value="50">
                    <span class="info">(Custom number input)</span>
                </div>

            </form>
        </div>
        
        <div class="test-container">
            <h3>🧪 Other Module Test Form</h3>
            <form class="redco-module-form test-form" data-module="page-cache">
                
                <div class="form-group">
                    <label for="cache_enabled">Enable Page Cache:</label>
                    <input type="checkbox" id="cache_enabled" name="settings[enabled]">
                    <span class="info">(Should save directly)</span>
                </div>
                
                <div class="form-group">
                    <label for="cache_duration">Cache Duration:</label>
                    <select id="cache_duration" name="settings[cache_duration]">
                        <option value="3600">1 Hour</option>
                        <option value="7200">2 Hours</option>
                        <option value="86400">24 Hours</option>
                    </select>
                    <span class="info">(Should save directly)</span>
                </div>
                
            </form>
        </div>
        
        <div class="test-container">
            <h3>🔍 Test Controls</h3>
            <button type="button" class="test-button" onclick="testStatusIndicators()">Test Status Indicators</button>
            <button type="button" class="test-button" onclick="testMemoryThresholds()">Test Memory Thresholds</button>
            <button type="button" class="test-button" onclick="testFormDetection()">Test Form Detection</button>
            <button type="button" class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-container">
            <h3>📋 Test Results</h3>
            <div id="test-log" class="log-output">Ready to run tests...\n</div>
        </div>
        
        <div class="status-check">
            <h3>📊 Expected Results After Fixes</h3>
            <ul>
                <li><strong>Memory Thresholds:</strong> Should show 95% and 98% instead of 0.95% and 0.98%</li>
                <li><strong>Simple Fields:</strong> enabled, lazy_load should save directly with green "Saved" indicators</li>
                <li><strong>Memory-Intensive Fields:</strong> minify_css, minify_js, critical_css may be queued only if memory actually exceeds thresholds</li>
                <li><strong>Status Indicators:</strong> Should appear next to ALL form fields in ALL modules</li>
                <li><strong>No Excessive Queuing:</strong> Only truly memory-intensive operations should be queued</li>
            </ul>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Simulate the redcoAjax object
        window.redcoAjax = {
            ajaxurl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce-12345',
            global_auto_save_nonce: 'test-auto-save-nonce-67890'
        };
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#test-log').append(`[${timestamp}] ${message}\n`);
            $('#test-log').scrollTop($('#test-log')[0].scrollHeight);
        }
        
        function clearLog() {
            $('#test-log').text('Log cleared...\n');
        }
        
        function testStatusIndicators() {
            log('🎯 Testing Status Indicators...');

            let indicatorsFound = 0;
            let fieldsFound = 0;

            // Enhanced field selector to match the JavaScript
            const fieldSelector = 'input, select, textarea, .redco-toggle-switch input, .threshold-slider, .enhanced-slider, .quality-slider, .redco-select, .redco-text-input, .redco-number-input';

            $(fieldSelector).each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                fieldsFound++;

                if (fieldName) {
                    // Check for existing indicators using multiple strategies
                    let hasIndicator = false;

                    // Check as sibling
                    if ($field.siblings('.redco-auto-save-status').length > 0) {
                        hasIndicator = true;
                    }

                    // Check in containers
                    if (!hasIndicator && $field.closest('.setting-item, .form-control, .setting-control, td').find('.redco-auto-save-status').length > 0) {
                        hasIndicator = true;
                    }

                    // Check after toggle container
                    if (!hasIndicator) {
                        const $toggleContainer = $field.closest('.redco-toggle-switch');
                        if ($toggleContainer.length > 0 && $toggleContainer.siblings('.redco-auto-save-status').length > 0) {
                            hasIndicator = true;
                        }
                    }

                    // Check after slider containers
                    if (!hasIndicator) {
                        const $sliderContainer = $field.closest('.threshold-slider-container, .quality-slider-container, .slider-container');
                        if ($sliderContainer.length > 0 && $sliderContainer.siblings('.redco-auto-save-status').length > 0) {
                            hasIndicator = true;
                        }
                    }

                    if (!hasIndicator) {
                        // Add status indicator using smart placement
                        const $indicator = $('<span class="redco-auto-save-status" role="status" aria-live="polite">' +
                            '<span class="status-icon" aria-hidden="true"></span>' +
                            '<span class="status-text"></span>' +
                            '<span class="sr-only"></span>' +
                            '</span>');

                        // Smart placement logic
                        if ($field.closest('.redco-toggle-switch').length > 0) {
                            $field.closest('.redco-toggle-switch').after($indicator);
                        } else if ($field.hasClass('threshold-slider') || $field.hasClass('enhanced-slider') || $field.hasClass('quality-slider')) {
                            const $container = $field.closest('.threshold-slider-container, .quality-slider-container, .slider-container');
                            if ($container.length > 0) {
                                $container.after($indicator);
                            } else {
                                $field.after($indicator);
                            }
                        } else {
                            $field.after($indicator);
                        }

                        log(`  ✅ Added status indicator to: ${fieldName} (${$field.attr('type') || $field.prop('tagName')})`);
                    } else {
                        log(`  ✅ Status indicator already exists for: ${fieldName} (${$field.attr('type') || $field.prop('tagName')})`);
                    }
                    indicatorsFound++;
                }
            });
            
            log(`📊 Status Indicators Test Results:`);
            log(`  - Fields found: ${fieldsFound}`);
            log(`  - Indicators created/found: ${indicatorsFound}`);
            
            if (indicatorsFound === fieldsFound) {
                log('✅ Status indicators test PASSED');
            } else {
                log('❌ Status indicators test FAILED');
            }
            
            // Test indicator functionality
            const testField = $('input[name*="settings["]').first();
            if (testField.length > 0) {
                const $indicator = testField.siblings('.redco-auto-save-status');
                
                log('🧪 Testing indicator states...');
                
                // Test saving state
                $indicator.removeClass('saved error queued')
                          .addClass('saving')
                          .find('.status-text').text('Saving...');
                log('  🟠 Showing "Saving..." state');
                
                setTimeout(() => {
                    // Test saved state
                    $indicator.removeClass('saving')
                              .addClass('saved')
                              .find('.status-text').text('Saved');
                    log('  🟢 Showing "Saved" state');
                    
                    setTimeout(() => {
                        // Clear state
                        $indicator.removeClass('saved')
                                  .find('.status-text').text('');
                        log('  ✅ Indicator test complete');
                    }, 2000);
                }, 1000);
            }
        }
        
        function testMemoryThresholds() {
            log('🧠 Testing Memory Threshold Calculation...');
            
            // Simulate the PHP logic in JavaScript
            const config = {
                memory_limit_threshold: 0.95,
                memory_limit_threshold_high_memory: 0.98
            };
            
            function getMemoryThreshold(module, fieldName) {
                const fieldKey = fieldName.replace(/^settings\[([^\]]+)\]$/, '$1');
                const isMemoryIntensive = (module === 'asset-optimization' && 
                    ['minify_css', 'minify_js', 'critical_css'].includes(fieldKey));
                
                if (isMemoryIntensive) {
                    return config.memory_limit_threshold_high_memory * 100; // Convert to percentage
                } else {
                    return config.memory_limit_threshold * 100; // Convert to percentage
                }
            }
            
            const testCases = [
                { module: 'asset-optimization', field: 'settings[enabled]', expected: 95 },
                { module: 'asset-optimization', field: 'settings[lazy_load]', expected: 95 },
                { module: 'asset-optimization', field: 'settings[minify_css]', expected: 98 },
                { module: 'asset-optimization', field: 'settings[minify_js]', expected: 98 },
                { module: 'asset-optimization', field: 'settings[critical_css]', expected: 98 },
                { module: 'page-cache', field: 'settings[enabled]', expected: 95 }
            ];
            
            let passed = 0;
            let total = testCases.length;
            
            testCases.forEach(testCase => {
                const result = getMemoryThreshold(testCase.module, testCase.field);
                const status = result === testCase.expected ? '✅' : '❌';
                
                log(`  ${status} ${testCase.module}:${testCase.field} -> ${result}% (expected: ${testCase.expected}%)`);
                
                if (result === testCase.expected) {
                    passed++;
                }
            });
            
            log(`📊 Memory Threshold Test Results: ${passed}/${total} passed`);
            
            if (passed === total) {
                log('✅ Memory threshold calculation test PASSED');
            } else {
                log('❌ Memory threshold calculation test FAILED');
            }
        }
        
        function testFormDetection() {
            log('📋 Testing Form Detection...');
            
            const forms = $('.redco-module-form, form[data-module], .redco-settings-form, form.redco-form');
            log(`  Found ${forms.length} auto-save forms`);
            
            forms.each(function(index) {
                const $form = $(this);
                const module = $form.data('module');
                const classes = $form.attr('class');
                const fieldCount = $form.find('input, select, textarea').length;
                
                log(`  Form ${index + 1}: module="${module}", fields=${fieldCount}, classes="${classes}"`);
            });
            
            if (forms.length > 0) {
                log('✅ Form detection test PASSED');
            } else {
                log('❌ Form detection test FAILED - no forms detected');
            }
        }
        
        $(document).ready(function() {
            log('🔧 Auto-Save Fixes Test Page Loaded');
            log('💡 Use the test buttons above to verify the fixes');
            log('');
            
            // Auto-run basic tests
            setTimeout(() => {
                testFormDetection();
                testMemoryThresholds();
                testStatusIndicators();
            }, 1000);
        });
    </script>
</body>
</html>
