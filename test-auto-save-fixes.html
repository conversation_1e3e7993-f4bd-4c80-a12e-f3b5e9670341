<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Save Fixes Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-form {
            margin: 20px 0;
        }
        
        .form-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            min-width: 200px;
            font-weight: bold;
        }
        
        input, select, textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .status-check {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffa500; }
        .info { color: #007cba; }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        /* Include auto-save indicator styles */
        .redco-auto-save-status {
            display: inline-flex;
            align-items: center;
            margin-left: 8px;
            font-size: 12px;
            line-height: 1;
            vertical-align: middle;
        }
        
        .redco-auto-save-status .status-icon {
            width: 14px;
            height: 14px;
            margin-right: 4px;
            display: inline-block;
            border-radius: 50%;
            position: relative;
        }
        
        .redco-auto-save-status .status-text {
            color: #666;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .redco-auto-save-status.saving .status-icon {
            background-color: #ffa500;
            border: 2px solid #ff8c00;
        }
        
        .redco-auto-save-status.saving .status-text {
            color: #ff8c00;
        }
        
        .redco-auto-save-status.saved .status-icon {
            background-color: #28a745;
            border: 2px solid #1e7e34;
        }
        
        .redco-auto-save-status.saved .status-text {
            color: #1e7e34;
        }
        
        .redco-auto-save-status.error .status-icon {
            background-color: #dc3545;
            border: 2px solid #c82333;
        }
        
        .redco-auto-save-status.error .status-text {
            color: #c82333;
        }
        
        .redco-auto-save-status.queued .status-icon {
            background-color: #17a2b8;
            border: 2px solid #138496;
        }
        
        .redco-auto-save-status.queued .status-text {
            color: #138496;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #005a87;
        }

        /* Custom field type styles */
        .redco-toggle-switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .redco-toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .redco-toggle-switch input:checked + .toggle-slider {
            background-color: #2196F3;
        }

        .redco-toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .threshold-slider-container, .quality-slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
        }

        .threshold-slider, .enhanced-slider, .quality-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
        }

        .threshold-input {
            width: 80px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }

        .threshold-unit {
            font-size: 12px;
            color: #666;
        }

        .quality-display {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
            color: #007cba;
        }

        .redco-select, .redco-text-input, .redco-number-input {
            border: 2px solid #007cba;
            border-radius: 4px;
        }

        /* Toast Notification Styles */
        .redco-toast-container {
            position: fixed;
            top: 32px;
            right: 20px;
            z-index: 999999;
            pointer-events: none;
            max-width: 400px;
        }

        .redco-toast {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: auto;
            border-left: 4px solid #007cba;
            max-width: 100%;
            word-wrap: break-word;
        }

        .redco-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            gap: 10px;
        }

        .toast-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            flex-shrink: 0;
            position: relative;
        }

        .toast-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .toast-message {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            font-weight: 500;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            line-height: 1;
            color: #666;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .toast-close:hover {
            background: rgba(0, 0, 0, 0.1);
            color: #333;
        }

        /* Toast Types */
        .redco-toast-info {
            border-left-color: #007cba;
        }

        .redco-toast-info .toast-icon {
            background: #e7f3ff;
        }

        .redco-toast-info .toast-icon::before {
            background: #007cba;
            animation: pulse 1.5s ease-in-out infinite;
        }

        .redco-toast-success {
            border-left-color: #28a745;
        }

        .redco-toast-success .toast-icon {
            background: #d4edda;
        }

        .redco-toast-success .toast-icon::before {
            background: #28a745;
        }

        .redco-toast-success .toast-icon::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #28a745;
            font-size: 12px;
            font-weight: bold;
        }

        .redco-toast-warning {
            border-left-color: #ffc107;
        }

        .redco-toast-warning .toast-icon {
            background: #fff3cd;
        }

        .redco-toast-warning .toast-icon::before {
            background: #ffc107;
        }

        .redco-toast-warning .toast-icon::after {
            content: '!';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #ffc107;
            font-size: 12px;
            font-weight: bold;
        }

        .redco-toast-error {
            border-left-color: #dc3545;
        }

        .redco-toast-error .toast-icon {
            background: #f8d7da;
        }

        .redco-toast-error .toast-icon::before {
            background: #dc3545;
        }

        .redco-toast-error .toast-icon::after {
            content: '✕';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #dc3545;
            font-size: 10px;
            font-weight: bold;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
            50% {
                opacity: 0.7;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Auto-Save Fixes Test</h1>
        <p>Testing the fixes for memory threshold calculation and status indicators.</p>
        
        <div class="status-check">
            <h3>🚀 Universal Auto-Save System</h3>
            <ul>
                <li class="success">✅ <strong>Memory Threshold Bug Fixed:</strong> Thresholds now correctly return percentages (95%, 98%) instead of decimals (0.95%, 0.98%)</li>
                <li class="success">✅ <strong>Architecture Fixed:</strong> Memory check integrated into save handler instead of separate hook</li>
                <li class="success">✅ <strong>Universal Field Detection:</strong> Automatically detects ANY form field type without manual configuration</li>
                <li class="success">✅ <strong>Toast Notifications:</strong> Centralized user feedback system replaces individual field indicators</li>
                <li class="success">✅ <strong>Future-Proof:</strong> Works with new field types without code updates</li>
            </ul>
        </div>
        
        <div class="test-container">
            <h3>🧪 Asset Optimization Test Form</h3>
            <form class="redco-module-form test-form" data-module="asset-optimization">

                <div class="form-group">
                    <label for="test_enabled">Enable Asset Optimization:</label>
                    <input type="checkbox" id="test_enabled" name="settings[enabled]">
                    <span class="info">(Should save directly - NOT queued)</span>
                </div>

                <div class="form-group">
                    <label for="test_lazy_load">Enable Lazy Loading:</label>
                    <input type="checkbox" id="test_lazy_load" name="settings[lazy_load]">
                    <span class="info">(Should save directly - NOT queued)</span>
                </div>

                <div class="form-group">
                    <label for="test_minify_css">Enable CSS Minification:</label>
                    <input type="checkbox" id="test_minify_css" name="settings[minify_css]">
                    <span class="warning">(May be queued if memory > 95%)</span>
                </div>

                <div class="form-group">
                    <label for="test_minify_js">Enable JS Minification:</label>
                    <input type="checkbox" id="test_minify_js" name="settings[minify_js]">
                    <span class="warning">(May be queued if memory > 95%)</span>
                </div>

                <div class="form-group">
                    <label for="test_critical_css">Enable Critical CSS:</label>
                    <input type="checkbox" id="test_critical_css" name="settings[critical_css]">
                    <span class="warning">(May be queued if memory > 98%)</span>
                </div>

            </form>
        </div>

        <div class="test-container">
            <h3>🎛️ Custom Field Types Test</h3>
            <form class="redco-module-form test-form" data-module="lazy-load">

                <div class="form-group">
                    <label for="toggle_test">Toggle Switch Test:</label>
                    <div class="redco-toggle-switch">
                        <input type="checkbox" id="toggle_test" name="settings[toggle_enabled]">
                        <span class="toggle-slider"></span>
                    </div>
                    <span class="info">(Custom toggle switch)</span>
                </div>

                <div class="form-group">
                    <label for="range_test">Range Slider Test:</label>
                    <div class="threshold-slider-container">
                        <input type="range" id="range_test" name="settings[threshold]" min="0" max="1000" value="200" class="threshold-slider">
                        <input type="number" name="settings[threshold_number]" value="200" class="threshold-input">
                        <span class="threshold-unit">px</span>
                    </div>
                    <span class="info">(Range slider with number input)</span>
                </div>

                <div class="form-group">
                    <label for="quality_test">Quality Slider Test:</label>
                    <div class="quality-slider-container">
                        <input type="range" id="quality_test" name="settings[quality]" min="1" max="100" value="85" class="enhanced-slider">
                        <div class="quality-display">
                            <span id="quality-value">85</span>%
                        </div>
                    </div>
                    <span class="info">(Quality slider)</span>
                </div>

                <div class="form-group">
                    <label for="custom_select">Custom Select:</label>
                    <select id="custom_select" name="settings[custom_option]" class="redco-select">
                        <option value="option1">Option 1</option>
                        <option value="option2">Option 2</option>
                        <option value="option3">Option 3</option>
                    </select>
                    <span class="info">(Custom select dropdown)</span>
                </div>

                <div class="form-group">
                    <label for="custom_text">Custom Text Input:</label>
                    <input type="text" id="custom_text" name="settings[custom_text]" class="redco-text-input" placeholder="Enter text...">
                    <span class="info">(Custom text input)</span>
                </div>

                <div class="form-group">
                    <label for="custom_number">Custom Number Input:</label>
                    <input type="number" id="custom_number" name="settings[custom_number]" class="redco-number-input" min="0" max="100" value="50">
                    <span class="info">(Custom number input)</span>
                </div>

            </form>
        </div>

        <div class="test-container">
            <h3>🔮 Future-Proof Field Types Test</h3>
            <form class="redco-module-form test-form" data-module="future-test">

                <div class="form-group">
                    <label for="contenteditable_test">Contenteditable Field:</label>
                    <div contenteditable="true" name="settings[content_field]" id="contenteditable_test" style="border: 1px solid #ddd; padding: 8px; min-height: 40px;">
                        Edit this content...
                    </div>
                    <span class="info">(Contenteditable element)</span>
                </div>

                <div class="form-group">
                    <label for="data_value_test">Data-Value Field:</label>
                    <div data-value="test-value" data-field="custom_data" name="settings[data_field]" id="data_value_test"
                         style="border: 1px solid #ddd; padding: 8px; cursor: pointer; background: #f9f9f9;"
                         onclick="this.dataset.value = this.dataset.value === 'enabled' ? 'disabled' : 'enabled'; this.textContent = 'Status: ' + this.dataset.value; $(this).trigger('change');">
                        Status: test-value
                    </div>
                    <span class="info">(Custom element with data-value)</span>
                </div>

                <div class="form-group">
                    <label for="aria_slider_test">ARIA Slider:</label>
                    <div role="slider" aria-valuemin="0" aria-valuemax="100" aria-valuenow="50"
                         name="settings[aria_slider]" id="aria_slider_test" tabindex="0"
                         style="width: 200px; height: 20px; background: #ddd; position: relative; border-radius: 10px; cursor: pointer;"
                         onclick="var rect = this.getBoundingClientRect(); var x = event.clientX - rect.left; var percent = Math.round((x / rect.width) * 100); this.setAttribute('aria-valuenow', percent); this.style.background = 'linear-gradient(to right, #007cba ' + percent + '%, #ddd ' + percent + '%)'; $(this).trigger('change');">
                        <span style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 12px; pointer-events: none;">50%</span>
                    </div>
                    <span class="info">(ARIA slider role)</span>
                </div>

                <div class="form-group">
                    <label for="unknown_future_field">Unknown Future Field:</label>
                    <custom-field-element name="settings[future_field]" id="unknown_future_field" value="future-value"
                                        style="display: inline-block; padding: 8px; border: 1px solid #ddd; background: #fff;">
                        Future Field Type
                    </custom-field-element>
                    <span class="info">(Hypothetical future custom element)</span>
                </div>

            </form>
        </div>
        
        <div class="test-container">
            <h3>🧪 Other Module Test Form</h3>
            <form class="redco-module-form test-form" data-module="page-cache">
                
                <div class="form-group">
                    <label for="cache_enabled">Enable Page Cache:</label>
                    <input type="checkbox" id="cache_enabled" name="settings[enabled]">
                    <span class="info">(Should save directly)</span>
                </div>
                
                <div class="form-group">
                    <label for="cache_duration">Cache Duration:</label>
                    <select id="cache_duration" name="settings[cache_duration]">
                        <option value="3600">1 Hour</option>
                        <option value="7200">2 Hours</option>
                        <option value="86400">24 Hours</option>
                    </select>
                    <span class="info">(Should save directly)</span>
                </div>
                
            </form>
        </div>
        
        <div class="test-container">
            <h3>🔍 Test Controls</h3>
            <button type="button" class="test-button" onclick="testUniversalFieldDetection()">Test Universal Field Detection</button>
            <button type="button" class="test-button" onclick="testToastNotifications()">Test Toast Notifications</button>
            <button type="button" class="test-button" onclick="testMemoryThresholds()">Test Memory Thresholds</button>
            <button type="button" class="test-button" onclick="testFormDetection()">Test Form Detection</button>
            <button type="button" class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div class="test-container">
            <h3>📋 Test Results</h3>
            <div id="test-log" class="log-output">Ready to run tests...\n</div>
        </div>
        
        <div class="status-check">
            <h3>🎯 Expected Results - Universal Auto-Save System</h3>
            <ul>
                <li><strong>Memory Thresholds:</strong> Should show 95% and 98% instead of 0.95% and 0.98%</li>
                <li><strong>Universal Field Detection:</strong> Should automatically detect ALL interactive elements (standard inputs, custom controls, contenteditable, ARIA elements, data-value elements)</li>
                <li><strong>Toast Notifications:</strong> Should show centralized notifications in top-right corner instead of individual field indicators</li>
                <li><strong>Future-Proof:</strong> Should work with any new field type without code updates</li>
                <li><strong>User Experience:</strong> Clean interface with immediate "Saving..." toast, followed by "Settings saved successfully" toast</li>
                <li><strong>No Field Indicators:</strong> No individual status indicators next to fields - all feedback via toast notifications</li>
                <li><strong>Accessibility:</strong> Toast notifications with ARIA live regions and screen reader support</li>
            </ul>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // Simulate the redcoAjax object
        window.redcoAjax = {
            ajaxurl: '/wp-admin/admin-ajax.php',
            nonce: 'test-nonce-12345',
            global_auto_save_nonce: 'test-auto-save-nonce-67890'
        };
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#test-log').append(`[${timestamp}] ${message}\n`);
            $('#test-log').scrollTop($('#test-log')[0].scrollHeight);
        }
        
        function clearLog() {
            $('#test-log').text('Log cleared...\n');
        }
        
        function testUniversalFieldDetection() {
            log('🔮 Testing Universal Field Detection...');

            let fieldsDetected = 0;
            let totalElements = 0;

            // Test the universal detection logic
            $('.redco-module-form').each(function() {
                const $form = $(this);
                const module = $form.data('module');
                log(`  📋 Testing form: ${module}`);

                $form.find('*').each(function() {
                    totalElements++;
                    const $element = $(this);

                    if (isInteractiveFormElement($element)) {
                        fieldsDetected++;
                        const tagName = $element.prop('tagName').toLowerCase();
                        const type = $element.attr('type') || 'N/A';
                        const name = $element.attr('name') || 'unnamed';
                        const role = $element.attr('role') || 'N/A';
                        const contenteditable = $element.attr('contenteditable') || 'N/A';

                        log(`    ✅ Detected: ${name} (${tagName}, type=${type}, role=${role}, contenteditable=${contenteditable})`);
                    }
                });
            });

            log(`📊 Universal Detection Results:`);
            log(`  - Total elements scanned: ${totalElements}`);
            log(`  - Interactive fields detected: ${fieldsDetected}`);

            if (fieldsDetected > 0) {
                log('✅ Universal field detection test PASSED');
            } else {
                log('❌ Universal field detection test FAILED');
            }
        }

        // Replicate the JavaScript detection logic for testing
        function isInteractiveFormElement($element) {
            const tagName = $element.prop('tagName').toLowerCase();
            const type = $element.attr('type');

            // Standard form elements
            if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') {
                return true;
            }

            // Elements with contenteditable
            if ($element.attr('contenteditable') === 'true') {
                return true;
            }

            // Elements that have a name attribute and can trigger change events
            if ($element.attr('name') && canTriggerChangeEvents($element)) {
                return true;
            }

            // Elements with data attributes indicating they're form controls
            if ($element.data('field') || $element.data('setting') || $element.data('value')) {
                return true;
            }

            // Elements with ARIA roles indicating form controls
            const role = $element.attr('role');
            if (role && ['slider', 'spinbutton', 'textbox', 'combobox', 'listbox', 'checkbox', 'radio', 'switch'].includes(role)) {
                return true;
            }

            return false;
        }

        function canTriggerChangeEvents($element) {
            try {
                // Test if element supports addEventListener for change events
                const hasChangeEvent = 'onchange' in $element[0] || 'oninput' in $element[0];

                // Check if element has event handlers that suggest it's interactive
                const hasEventHandlers = $element.data('events') ||
                                        $element.attr('onclick') ||
                                        $element.attr('onchange') ||
                                        $element.attr('oninput');

                return hasChangeEvent || hasEventHandlers;
            } catch (e) {
                return false;
            }
        }

        function testToastNotifications() {
            log('🍞 Testing Toast Notification System...');

            // Test different toast types
            showTestToast('Testing info toast - Saving changes...', 'info');

            setTimeout(() => {
                showTestToast('Testing success toast - Settings saved successfully!', 'success');
            }, 1000);

            setTimeout(() => {
                showTestToast('Testing warning toast - Save queued for processing', 'warning');
            }, 2000);

            setTimeout(() => {
                showTestToast('Testing error toast - Save failed, please try again', 'error');
            }, 3000);

            log('✅ Toast notifications test initiated - check top-right corner');
        }

        function showTestToast(message, type) {
            // Create toast container if it doesn't exist
            if ($('.redco-toast-container').length === 0) {
                $('body').append('<div class="redco-toast-container" aria-live="polite" aria-label="Notifications"></div>');
            }

            const $container = $('.redco-toast-container');

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const $toast = $(`
                <div class="redco-toast redco-toast-${type}" id="${toastId}" role="alert" aria-live="assertive">
                    <div class="toast-content">
                        <span class="toast-icon" aria-hidden="true"></span>
                        <span class="toast-message">${message}</span>
                        <button class="toast-close" aria-label="Close notification" type="button">&times;</button>
                    </div>
                </div>
            `);

            // Add toast to container
            $container.append($toast);

            // Animate in
            setTimeout(() => {
                $toast.addClass('show');
            }, 10);

            // Auto-remove after duration
            const removeToast = () => {
                $toast.removeClass('show');
                setTimeout(() => {
                    $toast.remove();
                }, 300);
            };

            // Set auto-remove timer
            const timer = setTimeout(removeToast, 4000);

            // Handle manual close
            $toast.find('.toast-close').on('click', () => {
                clearTimeout(timer);
                removeToast();
            });
        }
            
            log(`📊 Status Indicators Test Results:`);
            log(`  - Fields found: ${fieldsFound}`);
            log(`  - Indicators created/found: ${indicatorsFound}`);
            
            if (indicatorsFound === fieldsFound) {
                log('✅ Status indicators test PASSED');
            } else {
                log('❌ Status indicators test FAILED');
            }
            
            // Test indicator functionality
            const testField = $('input[name*="settings["]').first();
            if (testField.length > 0) {
                const $indicator = testField.siblings('.redco-auto-save-status');
                
                log('🧪 Testing indicator states...');
                
                // Test saving state
                $indicator.removeClass('saved error queued')
                          .addClass('saving')
                          .find('.status-text').text('Saving...');
                log('  🟠 Showing "Saving..." state');
                
                setTimeout(() => {
                    // Test saved state
                    $indicator.removeClass('saving')
                              .addClass('saved')
                              .find('.status-text').text('Saved');
                    log('  🟢 Showing "Saved" state');
                    
                    setTimeout(() => {
                        // Clear state
                        $indicator.removeClass('saved')
                                  .find('.status-text').text('');
                        log('  ✅ Indicator test complete');
                    }, 2000);
                }, 1000);
            }
        }
        
        function testMemoryThresholds() {
            log('🧠 Testing Memory Threshold Calculation...');
            
            // Simulate the PHP logic in JavaScript
            const config = {
                memory_limit_threshold: 0.95,
                memory_limit_threshold_high_memory: 0.98
            };
            
            function getMemoryThreshold(module, fieldName) {
                const fieldKey = fieldName.replace(/^settings\[([^\]]+)\]$/, '$1');
                const isMemoryIntensive = (module === 'asset-optimization' && 
                    ['minify_css', 'minify_js', 'critical_css'].includes(fieldKey));
                
                if (isMemoryIntensive) {
                    return config.memory_limit_threshold_high_memory * 100; // Convert to percentage
                } else {
                    return config.memory_limit_threshold * 100; // Convert to percentage
                }
            }
            
            const testCases = [
                { module: 'asset-optimization', field: 'settings[enabled]', expected: 95 },
                { module: 'asset-optimization', field: 'settings[lazy_load]', expected: 95 },
                { module: 'asset-optimization', field: 'settings[minify_css]', expected: 98 },
                { module: 'asset-optimization', field: 'settings[minify_js]', expected: 98 },
                { module: 'asset-optimization', field: 'settings[critical_css]', expected: 98 },
                { module: 'page-cache', field: 'settings[enabled]', expected: 95 }
            ];
            
            let passed = 0;
            let total = testCases.length;
            
            testCases.forEach(testCase => {
                const result = getMemoryThreshold(testCase.module, testCase.field);
                const status = result === testCase.expected ? '✅' : '❌';
                
                log(`  ${status} ${testCase.module}:${testCase.field} -> ${result}% (expected: ${testCase.expected}%)`);
                
                if (result === testCase.expected) {
                    passed++;
                }
            });
            
            log(`📊 Memory Threshold Test Results: ${passed}/${total} passed`);
            
            if (passed === total) {
                log('✅ Memory threshold calculation test PASSED');
            } else {
                log('❌ Memory threshold calculation test FAILED');
            }
        }
        
        function testFormDetection() {
            log('📋 Testing Form Detection...');
            
            const forms = $('.redco-module-form, form[data-module], .redco-settings-form, form.redco-form');
            log(`  Found ${forms.length} auto-save forms`);
            
            forms.each(function(index) {
                const $form = $(this);
                const module = $form.data('module');
                const classes = $form.attr('class');
                const fieldCount = $form.find('input, select, textarea').length;
                
                log(`  Form ${index + 1}: module="${module}", fields=${fieldCount}, classes="${classes}"`);
            });
            
            if (forms.length > 0) {
                log('✅ Form detection test PASSED');
            } else {
                log('❌ Form detection test FAILED - no forms detected');
            }
        }
        
        $(document).ready(function() {
            log('🚀 Universal Auto-Save System Test Page Loaded');
            log('💡 Use the test buttons above to verify the universal system');
            log('');

            // Auto-run basic tests
            setTimeout(() => {
                testFormDetection();
                testMemoryThresholds();
                testUniversalFieldDetection();
            }, 1000);
        });
    </script>
</body>
</html>
