/**
 * Auto-Save Fix Verification
 * 
 * Quick verification that the auto-save fixes are working
 */

(function($) {
    'use strict';

    window.RedcoAutoSaveFixVerification = {
        
        // Run verification
        runVerification: function() {
            console.log('🔧 Verifying auto-save fixes...');
            
            this.createVerificationInterface();
            this.testDirectSaveWorking();
        },

        // Create verification interface
        createVerificationInterface: function() {
            if ($('#redco-fix-verification').length === 0) {
                const verificationInterface = `
                    <div id="redco-fix-verification" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border: 3px solid #28a745; padding: 25px; z-index: 10000; width: 500px; box-shadow: 0 8px 25px rgba(0,0,0,0.2); border-radius: 8px;">
                        <h3 style="margin-top: 0; color: #28a745;">✅ Auto-Save Fix Verification</h3>
                        <div id="verification-status" style="margin: 15px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; font-weight: bold;">Testing fixes...</div>
                        
                        <div id="test-results" style="margin: 15px 0; max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background: #f8f9fa; border-radius: 4px; font-size: 14px;">
                            <div id="results-content">Starting verification...</div>
                        </div>
                        
                        <div style="margin: 15px 0;">
                            <button type="button" id="test-normal-save" style="background: #28a745; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">Test Normal Save</button>
                            <button type="button" id="test-memory-intensive" style="background: #ffc107; color: black; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">Test Memory-Intensive</button>
                            <button type="button" id="check-queue" style="background: #007cba; color: white; border: none; padding: 10px 15px; border-radius: 4px; cursor: pointer; margin-right: 10px;">Check Queue</button>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <button type="button" id="close-verification" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; font-size: 14px;">Close</button>
                        </div>
                    </div>
                `;
                
                $('body').append(verificationInterface);
                this.bindVerificationEvents();
            }
        },

        // Bind verification events
        bindVerificationEvents: function() {
            const self = this;
            
            $('#close-verification').on('click', function() {
                $('#redco-fix-verification').remove();
            });
            
            $('#test-normal-save').on('click', function() {
                self.testNormalSave();
            });
            
            $('#test-memory-intensive').on('click', function() {
                self.testMemoryIntensiveSave();
            });
            
            $('#check-queue').on('click', function() {
                self.checkQueueStatus();
            });
        },

        // Test that direct save is working
        testDirectSaveWorking: function() {
            this.updateStatus('Testing if normal saves work directly...');
            this.addResult('🧪 Testing normal auto-save operation...');
            
            // Test a simple save that should NOT be queued
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_global_auto_save',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: 'page-cache',
                    field_name: 'settings[test_fix_verification]',
                    field_value: 'test_value_' + Date.now()
                },
                success: (response) => {
                    if (response.success) {
                        if (response.data.queued) {
                            this.addResult('❌ ISSUE STILL EXISTS: Normal operation was queued');
                            this.addResult('💡 Try running the diagnostic tool: diagnoseAutoSave()');
                            this.updateStatus('❌ Auto-save fix needs attention');
                        } else {
                            this.addResult('✅ SUCCESS: Normal operation saved directly');
                            this.updateStatus('✅ Auto-save fix is working!');
                            this.testMemoryIntensiveSave();
                        }
                    } else {
                        this.addResult('❌ ERROR: ' + (response.data.message || 'Unknown error'));
                        this.updateStatus('❌ Auto-save has errors');
                    }
                },
                error: (xhr, status, error) => {
                    this.addResult('❌ NETWORK ERROR: ' + error);
                    this.updateStatus('❌ Network error during test');
                }
            });
        },

        // Test normal save
        testNormalSave: function() {
            this.addResult('🧪 Testing normal save (should save directly)...');
            
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_global_auto_save',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: 'heartbeat-control',
                    field_name: 'settings[test_normal_save]',
                    field_value: 'normal_test_' + Date.now()
                },
                success: (response) => {
                    if (response.success) {
                        if (response.data.queued) {
                            this.addResult('❌ Normal save was queued (should save directly)');
                        } else {
                            this.addResult('✅ Normal save worked directly');
                        }
                    } else {
                        this.addResult('❌ Normal save failed: ' + (response.data.message || 'Unknown error'));
                    }
                },
                error: () => {
                    this.addResult('❌ Normal save network error');
                }
            });
        },

        // Test memory-intensive save
        testMemoryIntensiveSave: function() {
            this.addResult('🧠 Testing memory-intensive save (may be queued)...');
            
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_global_auto_save',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: 'asset-optimization',
                    field_name: 'settings[minify_css]',
                    field_value: true
                },
                success: (response) => {
                    if (response.success) {
                        if (response.data.queued) {
                            this.addResult('✅ Memory-intensive save was queued (expected behavior)');
                            this.addResult('📋 Queue processing should complete within 30-60 seconds');
                        } else {
                            this.addResult('✅ Memory-intensive save completed directly (sufficient memory available)');
                        }
                    } else {
                        this.addResult('❌ Memory-intensive save failed: ' + (response.data.message || 'Unknown error'));
                    }
                },
                error: () => {
                    this.addResult('❌ Memory-intensive save network error');
                }
            });
        },

        // Check queue status
        checkQueueStatus: function() {
            this.addResult('📋 Checking queue status...');
            
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_get_queue_status',
                    nonce: redcoAjax.nonce
                },
                success: (response) => {
                    if (response.success) {
                        const status = response.data;
                        this.addResult(`📊 Queue has ${status.queue_size} items`);
                        this.addResult(`⏰ Cron scheduled: ${status.cron_scheduled ? 'Yes' : 'No'}`);
                        this.addResult(`🚫 Cron disabled: ${status.cron_disabled ? 'Yes' : 'No'}`);
                        
                        if (status.queue_size > 0) {
                            this.addResult('💡 You can force process the queue with the diagnostic tool');
                        }
                    } else {
                        this.addResult('❌ Could not get queue status');
                    }
                },
                error: () => {
                    this.addResult('❌ Queue status check failed');
                }
            });
        },

        // Update status
        updateStatus: function(status) {
            $('#verification-status').html(status);
        },

        // Add result to display
        addResult: function(result) {
            const $results = $('#results-content');
            $results.append(`<div style="margin: 5px 0; padding: 5px; border-left: 3px solid #28a745;">${result}</div>`);
            $results.scrollTop($results[0].scrollHeight);
            console.log(`Fix Verification: ${result}`);
        }
    };

    // Auto-run verification if there were auto-save issues
    $(document).ready(function() {
        // Check if we should auto-run verification
        if (window.location.href.includes('redco_verify_fix=1')) {
            setTimeout(() => {
                RedcoAutoSaveFixVerification.runVerification();
            }, 3000);
        }
    });

    // Add console helper
    window.verifyAutoSaveFix = function() {
        RedcoAutoSaveFixVerification.runVerification();
    };

})(jQuery);

console.log('✅ Auto-Save Fix Verification loaded');
console.log('💡 Use verifyAutoSaveFix() to verify the auto-save fixes are working');
