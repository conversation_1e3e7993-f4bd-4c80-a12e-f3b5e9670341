/**
 * Memory Optimization Verification Script
 * 
 * Comprehensive verification of all memory optimizations implemented
 */

(function($) {
    'use strict';

    window.RedcoMemoryOptimizationVerification = {
        
        // Verification results
        results: {
            streamingProcessing: null,
            batchedOperations: null,
            databaseOptimization: null,
            memoryProfiling: null,
            overallScore: 0
        },

        // Run comprehensive verification
        runVerification: function() {
            console.log('🔍 Starting Memory Optimization Verification...');
            
            this.createVerificationInterface();
            
            const verificationTests = [
                this.verifyStreamingProcessing,
                this.verifyBatchedOperations,
                this.verifyDatabaseOptimization,
                this.verifyMemoryProfiling,
                this.verifyOverallPerformance
            ];

            let testIndex = 0;
            const runNextTest = () => {
                if (testIndex < verificationTests.length) {
                    const test = verificationTests[testIndex];
                    this.updateStatus(`Running test ${testIndex + 1}/${verificationTests.length}: ${test.name}`);
                    
                    test.call(this)
                        .then(() => {
                            testIndex++;
                            setTimeout(runNextTest, 1000);
                        })
                        .catch((error) => {
                            console.error(`Test ${test.name} failed:`, error);
                            testIndex++;
                            setTimeout(runNextTest, 1000);
                        });
                } else {
                    this.generateFinalReport();
                }
            };

            runNextTest();
        },

        // Create verification interface
        createVerificationInterface: function() {
            if ($('#redco-verification-interface').length === 0) {
                const interfaceHtml = `
                    <div id="redco-verification-interface" style="position: fixed; top: 50px; left: 50%; transform: translateX(-50%); background: white; border: 3px solid #28a745; padding: 25px; z-index: 10000; width: 600px; max-height: 80vh; overflow-y: auto; box-shadow: 0 8px 25px rgba(0,0,0,0.2); border-radius: 8px;">
                        <h3 style="margin-top: 0; color: #28a745;">🔍 Memory Optimization Verification</h3>
                        <div id="verification-status" style="margin: 15px 0; padding: 10px; background: #e7f3ff; border-left: 4px solid #007cba; font-weight: bold;">Initializing verification...</div>
                        
                        <div id="verification-progress" style="margin: 15px 0;">
                            <div style="background: #f0f0f0; height: 20px; border-radius: 10px; overflow: hidden;">
                                <div id="progress-bar" style="background: linear-gradient(90deg, #28a745, #20c997); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                            <div id="progress-text" style="text-align: center; margin-top: 5px; font-size: 12px; color: #666;">0% Complete</div>
                        </div>
                        
                        <div id="verification-results" style="margin: 20px 0; max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 15px; background: #f9f9f9; border-radius: 4px;">
                            <h4 style="margin-top: 0;">Verification Results:</h4>
                            <div id="results-list">Starting verification tests...</div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 20px;">
                            <button type="button" id="close-verification" style="background: #dc3545; color: white; border: none; padding: 12px 20px; border-radius: 4px; cursor: pointer; font-size: 14px;">Close</button>
                        </div>
                    </div>
                `;
                
                $('body').append(interfaceHtml);
                
                $('#close-verification').on('click', function() {
                    $('#redco-verification-interface').remove();
                });
            }
        },

        // Verify streaming processing optimization
        verifyStreamingProcessing: function() {
            return new Promise((resolve) => {
                this.addResult('🔄 Testing streaming file processing optimization...');
                
                // Test large file handling
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_global_auto_save',
                        nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                        module: 'asset-optimization',
                        field_name: 'settings[minify_css]',
                        field_value: true
                    },
                    success: (response) => {
                        if (response.success) {
                            if (response.data.queued) {
                                this.addResult('✅ Streaming processing: Large file operation queued (optimization working)');
                                this.results.streamingProcessing = 'optimized';
                            } else {
                                this.addResult('✅ Streaming processing: Operation completed efficiently');
                                this.results.streamingProcessing = 'efficient';
                            }
                        } else {
                            if (response.data.code === 'MEMORY_LIMIT_EXCEEDED') {
                                this.addResult('❌ Streaming processing: Memory limit still exceeded (needs optimization)');
                                this.results.streamingProcessing = 'needs_work';
                            } else {
                                this.addResult('⚠️ Streaming processing: Other error occurred');
                                this.results.streamingProcessing = 'error';
                            }
                        }
                        resolve();
                    },
                    error: () => {
                        this.addResult('❌ Streaming processing: Network error during test');
                        this.results.streamingProcessing = 'error';
                        resolve();
                    }
                });
            });
        },

        // Verify batched operations
        verifyBatchedOperations: function() {
            return new Promise((resolve) => {
                this.addResult('📦 Testing batched cache clearing operations...');
                
                // Test cache clearing
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_clear_asset_cache',
                        nonce: redcoAjax.nonce
                    },
                    success: (response) => {
                        if (response.success) {
                            this.addResult('✅ Batched operations: Cache clearing completed successfully');
                            this.results.batchedOperations = 'working';
                        } else {
                            this.addResult('❌ Batched operations: Cache clearing failed');
                            this.results.batchedOperations = 'failed';
                        }
                        resolve();
                    },
                    error: () => {
                        this.addResult('❌ Batched operations: Network error during cache clearing test');
                        this.results.batchedOperations = 'error';
                        resolve();
                    }
                });
            });
        },

        // Verify database optimization
        verifyDatabaseOptimization: function() {
            return new Promise((resolve) => {
                this.addResult('🗄️ Testing database operation optimization...');
                
                // Test multiple rapid saves to verify database batching
                const testPromises = [];
                for (let i = 0; i < 3; i++) {
                    testPromises.push(
                        $.ajax({
                            url: redcoAjax.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'redco_global_auto_save',
                                nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                                module: 'asset-optimization',
                                field_name: `settings[test_db_${i}]`,
                                field_value: `test_value_${i}`
                            }
                        })
                    );
                }
                
                Promise.allSettled(testPromises)
                    .then((results) => {
                        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
                        
                        if (successCount >= 2) {
                            this.addResult('✅ Database optimization: Multiple operations handled efficiently');
                            this.results.databaseOptimization = 'optimized';
                        } else {
                            this.addResult('⚠️ Database optimization: Some operations failed');
                            this.results.databaseOptimization = 'partial';
                        }
                        resolve();
                    });
            });
        },

        // Verify memory profiling
        verifyMemoryProfiling: function() {
            return new Promise((resolve) => {
                this.addResult('📊 Testing memory profiling capabilities...');
                
                // Check if memory profiler is available
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_check_memory_profiler',
                        nonce: redcoAjax.nonce
                    },
                    success: (response) => {
                        if (response.success && response.data.profiler_available) {
                            this.addResult('✅ Memory profiling: Profiler available and active');
                            this.results.memoryProfiling = 'active';
                        } else {
                            this.addResult('⚠️ Memory profiling: Profiler not available (limited monitoring)');
                            this.results.memoryProfiling = 'limited';
                        }
                        resolve();
                    },
                    error: () => {
                        this.addResult('❌ Memory profiling: Error checking profiler status');
                        this.results.memoryProfiling = 'error';
                        resolve();
                    }
                });
            });
        },

        // Verify overall performance
        verifyOverallPerformance: function() {
            return new Promise((resolve) => {
                this.addResult('🎯 Testing overall memory optimization performance...');
                
                // Get current memory status
                $.ajax({
                    url: redcoAjax.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'redco_get_memory_status',
                        nonce: redcoAjax.nonce
                    },
                    success: (response) => {
                        if (response.success) {
                            const memory = response.data;
                            const usagePercent = memory.usage_percentage;
                            
                            this.addResult(`📈 Current memory usage: ${memory.current_usage_formatted} (${usagePercent.toFixed(1)}%)`);
                            this.addResult(`📊 Peak memory usage: ${memory.peak_usage_formatted} (${memory.peak_percentage.toFixed(1)}%)`);
                            this.addResult(`💾 Available memory: ${memory.available_memory_formatted}`);
                            
                            if (usagePercent < 70) {
                                this.addResult('✅ Overall performance: Excellent memory efficiency');
                                this.results.overallScore = 95;
                            } else if (usagePercent < 80) {
                                this.addResult('✅ Overall performance: Good memory efficiency');
                                this.results.overallScore = 85;
                            } else if (usagePercent < 90) {
                                this.addResult('⚠️ Overall performance: Moderate memory usage');
                                this.results.overallScore = 70;
                            } else {
                                this.addResult('❌ Overall performance: High memory usage detected');
                                this.results.overallScore = 50;
                            }
                        } else {
                            this.addResult('❌ Overall performance: Unable to get memory status');
                            this.results.overallScore = 60;
                        }
                        resolve();
                    },
                    error: () => {
                        this.addResult('❌ Overall performance: Error getting memory status');
                        this.results.overallScore = 50;
                        resolve();
                    }
                });
            });
        },

        // Generate final report
        generateFinalReport: function() {
            this.updateStatus('Generating final verification report...');
            this.updateProgress(100);
            
            this.addResult('');
            this.addResult('📋 FINAL VERIFICATION REPORT');
            this.addResult('================================');
            
            // Calculate overall score
            let totalScore = 0;
            let testCount = 0;
            
            const scoreMap = {
                'optimized': 100, 'efficient': 95, 'working': 90, 'active': 85,
                'partial': 70, 'limited': 60, 'needs_work': 40, 'failed': 20, 'error': 10
            };
            
            Object.keys(this.results).forEach(key => {
                if (key !== 'overallScore' && this.results[key]) {
                    totalScore += scoreMap[this.results[key]] || 50;
                    testCount++;
                }
            });
            
            totalScore += this.results.overallScore;
            testCount++;
            
            const averageScore = totalScore / testCount;
            
            this.addResult(`🎯 Overall Optimization Score: ${averageScore.toFixed(1)}/100`);
            
            if (averageScore >= 90) {
                this.addResult('🎉 EXCELLENT: Memory optimizations are working perfectly!');
            } else if (averageScore >= 80) {
                this.addResult('✅ GOOD: Memory optimizations are working well with minor areas for improvement');
            } else if (averageScore >= 70) {
                this.addResult('⚠️ MODERATE: Memory optimizations are partially working, some issues need attention');
            } else {
                this.addResult('❌ NEEDS WORK: Memory optimizations need significant improvement');
            }
            
            this.addResult('');
            this.addResult('📊 Detailed Results:');
            this.addResult(`• Streaming Processing: ${this.results.streamingProcessing || 'not tested'}`);
            this.addResult(`• Batched Operations: ${this.results.batchedOperations || 'not tested'}`);
            this.addResult(`• Database Optimization: ${this.results.databaseOptimization || 'not tested'}`);
            this.addResult(`• Memory Profiling: ${this.results.memoryProfiling || 'not tested'}`);
            
            this.updateStatus('Verification complete! Check results above.');
            
            console.log('🎉 Memory Optimization Verification Complete!');
            console.log('Results:', this.results);
        },

        // Update status
        updateStatus: function(status) {
            $('#verification-status').text(status);
        },

        // Update progress
        updateProgress: function(percent) {
            $('#progress-bar').css('width', percent + '%');
            $('#progress-text').text(percent + '% Complete');
        },

        // Add result to display
        addResult: function(result) {
            const $results = $('#results-list');
            $results.append(`<div style="margin: 5px 0; padding: 5px; border-left: 3px solid #007cba; background: #f8f9fa;">${result}</div>`);
            $results.scrollTop($results[0].scrollHeight);
            console.log(`Verification: ${result}`);
        }
    };

    // Auto-run verification in debug mode
    $(document).ready(function() {
        if (window.location.href.includes('redco_verify_memory=1')) {
            setTimeout(() => {
                RedcoMemoryOptimizationVerification.runVerification();
            }, 2000);
        }
    });

    // Add console helper
    window.verifyMemoryOptimizations = function() {
        RedcoMemoryOptimizationVerification.runVerification();
    };

})(jQuery);

console.log('🔍 Memory Optimization Verification loaded');
console.log('💡 Use verifyMemoryOptimizations() to run comprehensive verification');
