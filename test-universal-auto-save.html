<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Auto-Save Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            min-width: 150px;
            font-weight: bold;
        }
        
        input, select, textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        /* Toast Styles */
        .redco-toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            max-width: 400px;
        }

        .redco-toast {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            border-left: 4px solid #007cba;
            max-width: 100%;
        }

        .redco-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            gap: 10px;
        }

        .toast-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #e7f3ff;
        }

        .toast-message {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            width: 20px;
            height: 20px;
        }

        .redco-toast-success {
            border-left-color: #28a745;
        }

        .redco-toast-warning {
            border-left-color: #ffc107;
        }

        .redco-toast-error {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Universal Auto-Save System Test</h1>
        <p>Testing the new universal field detection and toast notification system.</p>
    </div>

    <div class="test-container">
        <h3>📋 Test Form</h3>
        <form class="redco-module-form" data-module="test-module">
            
            <div class="form-group">
                <label for="test_text">Text Input:</label>
                <input type="text" id="test_text" name="settings[text_field]" value="Test value">
            </div>
            
            <div class="form-group">
                <label for="test_checkbox">Checkbox:</label>
                <input type="checkbox" id="test_checkbox" name="settings[checkbox_field]">
            </div>
            
            <div class="form-group">
                <label for="test_select">Select:</label>
                <select id="test_select" name="settings[select_field]">
                    <option value="option1">Option 1</option>
                    <option value="option2">Option 2</option>
                    <option value="option3">Option 3</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="test_range">Range:</label>
                <input type="range" id="test_range" name="settings[range_field]" min="0" max="100" value="50">
            </div>
            
            <div class="form-group">
                <label for="test_contenteditable">Contenteditable:</label>
                <div contenteditable="true" name="settings[content_field]" id="test_contenteditable" 
                     style="border: 1px solid #ddd; padding: 8px; min-height: 40px;">
                    Edit this content...
                </div>
            </div>
            
            <div class="form-group">
                <label for="test_data_value">Data-Value Element:</label>
                <div data-value="test-value" name="settings[data_field]" id="test_data_value" 
                     style="border: 1px solid #ddd; padding: 8px; cursor: pointer; background: #f9f9f9;"
                     onclick="toggleDataValue(this)">
                    Click to toggle: test-value
                </div>
            </div>
            
        </form>
    </div>

    <div class="test-container">
        <h3>🔍 Test Controls</h3>
        <button type="button" class="test-button" onclick="testUniversalDetection()">Test Universal Detection</button>
        <button type="button" class="test-button" onclick="testToastSystem()">Test Toast System</button>
        <button type="button" class="test-button" onclick="simulateAutoSave()">Simulate Auto-Save</button>
        <button type="button" class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-container">
        <h3>📋 Test Results</h3>
        <div id="test-log" class="log-output">Ready to run tests...\n</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#test-log').append(`[${timestamp}] ${message}\n`);
            $('#test-log').scrollTop($('#test-log')[0].scrollHeight);
        }
        
        function clearLog() {
            $('#test-log').text('Log cleared...\n');
        }
        
        function toggleDataValue(element) {
            const currentValue = element.dataset.value;
            const newValue = currentValue === 'enabled' ? 'disabled' : 'enabled';
            element.dataset.value = newValue;
            element.textContent = `Click to toggle: ${newValue}`;
            $(element).trigger('change');
        }
        
        function testUniversalDetection() {
            log('🔮 Testing Universal Field Detection...');
            
            const $form = $('.redco-module-form');
            let detectedFields = 0;
            
            $form.find('*').each(function() {
                const $element = $(this);
                
                if (isInteractiveFormElement($element)) {
                    detectedFields++;
                    const tagName = $element.prop('tagName').toLowerCase();
                    const name = $element.attr('name') || 'unnamed';
                    const type = $element.attr('type') || 'N/A';
                    const contenteditable = $element.attr('contenteditable') || 'N/A';
                    const dataValue = $element.data('value') || 'N/A';
                    
                    log(`  ✅ Detected: ${name} (${tagName}, type=${type}, contenteditable=${contenteditable}, data-value=${dataValue})`);
                }
            });
            
            log(`📊 Detection Results: ${detectedFields} interactive fields found`);
            
            if (detectedFields > 0) {
                log('✅ Universal detection test PASSED');
            } else {
                log('❌ Universal detection test FAILED');
            }
        }
        
        function isInteractiveFormElement($element) {
            const tagName = $element.prop('tagName').toLowerCase();
            
            // Standard form elements
            if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') {
                return true;
            }
            
            // Elements with contenteditable
            if ($element.attr('contenteditable') === 'true') {
                return true;
            }
            
            // Elements with data attributes indicating they're form controls
            if ($element.data('field') || $element.data('setting') || $element.data('value')) {
                return true;
            }
            
            // Elements that have a name attribute
            if ($element.attr('name')) {
                return true;
            }
            
            return false;
        }
        
        function testToastSystem() {
            log('🍞 Testing Toast Notification System...');
            
            showToast('Testing info toast - Saving changes...', 'info');
            
            setTimeout(() => {
                showToast('Testing success toast - Settings saved!', 'success');
            }, 1000);
            
            setTimeout(() => {
                showToast('Testing warning toast - Save queued', 'warning');
            }, 2000);
            
            setTimeout(() => {
                showToast('Testing error toast - Save failed', 'error');
            }, 3000);
            
            log('✅ Toast notifications test initiated - check top-right corner');
        }
        
        function showToast(message, type) {
            // Create toast container if it doesn't exist
            if ($('.redco-toast-container').length === 0) {
                $('body').append('<div class="redco-toast-container"></div>');
            }
            
            const $container = $('.redco-toast-container');
            
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const $toast = $(`
                <div class="redco-toast redco-toast-${type}" id="${toastId}">
                    <div class="toast-content">
                        <span class="toast-icon"></span>
                        <span class="toast-message">${message}</span>
                        <button class="toast-close" onclick="closeToast('${toastId}')">&times;</button>
                    </div>
                </div>
            `);
            
            // Add toast to container
            $container.append($toast);
            
            // Animate in
            setTimeout(() => {
                $toast.addClass('show');
            }, 10);
            
            // Auto-remove after 4 seconds
            setTimeout(() => {
                closeToast(toastId);
            }, 4000);
        }
        
        function closeToast(toastId) {
            const $toast = $('#' + toastId);
            $toast.removeClass('show');
            setTimeout(() => {
                $toast.remove();
            }, 300);
        }
        
        function simulateAutoSave() {
            log('💾 Simulating Auto-Save Process...');
            
            showToast('Saving changes...', 'info');
            
            setTimeout(() => {
                showToast('Settings saved successfully', 'success');
                log('✅ Auto-save simulation completed');
            }, 2000);
        }
        
        $(document).ready(function() {
            log('🚀 Universal Auto-Save Test Page Loaded');
            log('💡 Use the test buttons to verify the system');
            log('');
            
            // Auto-run detection test
            setTimeout(() => {
                testUniversalDetection();
            }, 500);
        });
    </script>
</body>
</html>
