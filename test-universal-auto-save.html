<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Auto-Save Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin: 15px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        label {
            min-width: 150px;
            font-weight: bold;
        }
        
        input, select, textarea {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        .log-output {
            background: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        /* Toast Styles */
        .redco-toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            max-width: 400px;
        }

        .redco-toast {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            border-left: 4px solid #007cba;
            max-width: 100%;
        }

        .redco-toast.show {
            opacity: 1;
            transform: translateX(0);
        }

        .toast-content {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            gap: 10px;
        }

        .toast-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #e7f3ff;
        }

        .toast-message {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .toast-close {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            width: 20px;
            height: 20px;
        }

        .redco-toast-success {
            border-left-color: #28a745;
        }

        .redco-toast-warning {
            border-left-color: #ffc107;
        }

        .redco-toast-error {
            border-left-color: #dc3545;
        }

        /* Update Animation for Single Toast */
        .redco-toast.updating {
            transform: scale(1.02);
            transition: transform 0.2s ease;
        }

        .redco-toast.updating .toast-content {
            animation: contentUpdate 0.2s ease;
        }

        @keyframes contentUpdate {
            0% {
                opacity: 0.8;
            }
            100% {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🚀 Universal Auto-Save System Test</h1>
        <p>Testing the new universal field detection and toast notification system.</p>
    </div>

    <div class="test-container">
        <h3>🎯 Lazy Load Threshold Controls Test</h3>
        <form class="redco-module-form" data-module="lazy-load">

            <div class="form-group">
                <label for="threshold">Loading Threshold Distance:</label>
                <input type="number" id="threshold" name="settings[threshold]" value="200" min="0" max="2000">
                <span class="info">(Main threshold input)</span>
            </div>

            <div class="form-group">
                <label>Threshold Presets:</label>
                <div style="display: flex; gap: 10px; margin-top: 5px;">
                    <button type="button" class="threshold-preset test-button" data-value="0" style="background: #28a745; font-size: 11px; padding: 8px;">Maximum Savings<br>0px</button>
                    <button type="button" class="threshold-preset test-button" data-value="200" style="background: #17a2b8; font-size: 11px; padding: 8px;">Balanced<br>200px</button>
                    <button type="button" class="threshold-preset test-button" data-value="500" style="background: #ffc107; color: #000; font-size: 11px; padding: 8px;">Smooth Experience<br>500px</button>
                </div>
                <span class="info">(Threshold preset buttons - should trigger auto-save)</span>
            </div>

            <div class="form-group">
                <label for="threshold-slider">Threshold Slider:</label>
                <input type="range" id="threshold-slider" class="threshold-slider" min="0" max="1000" value="200" style="width: 100%;">
                <span class="info">(Threshold slider control - should trigger auto-save)</span>
            </div>

        </form>
    </div>

    <div class="test-container">
        <h3>🏗️ Asset Optimization Test</h3>
        <form class="redco-module-form" data-module="asset-optimization">

            <div class="form-group">
                <label for="asset_enabled">Enable Asset Optimization:</label>
                <input type="checkbox" id="asset_enabled" name="settings[enabled]">
                <span class="info">(Should save directly - NOT memory intensive)</span>
            </div>

            <div class="form-group">
                <label for="lazy_load_enabled">Enable Lazy Loading:</label>
                <input type="checkbox" id="lazy_load_enabled" name="settings[lazy_load]">
                <span class="info">(Should save directly - NOT memory intensive)</span>
            </div>

            <div class="form-group">
                <label for="minify_css_enabled">Enable CSS Minification:</label>
                <input type="checkbox" id="minify_css_enabled" name="settings[minify_css]">
                <span class="warning">(Memory intensive - may be queued if memory > 98%)</span>
            </div>

            <div class="form-group">
                <label for="minify_js_enabled">Enable JS Minification:</label>
                <input type="checkbox" id="minify_js_enabled" name="settings[minify_js]">
                <span class="warning">(Memory intensive - may be queued if memory > 98%)</span>
            </div>

        </form>
    </div>

    <div class="test-container">
        <h3>📋 Standard Test Form</h3>
        <form class="redco-module-form" data-module="test-module">
            
            <div class="form-group">
                <label for="test_text">Text Input:</label>
                <input type="text" id="test_text" name="settings[text_field]" value="Test value">
            </div>
            
            <div class="form-group">
                <label for="test_checkbox">Checkbox:</label>
                <input type="checkbox" id="test_checkbox" name="settings[checkbox_field]">
            </div>
            
            <div class="form-group">
                <label for="test_select">Select:</label>
                <select id="test_select" name="settings[select_field]">
                    <option value="option1">Option 1</option>
                    <option value="option2">Option 2</option>
                    <option value="option3">Option 3</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="test_range">Range:</label>
                <input type="range" id="test_range" name="settings[range_field]" min="0" max="100" value="50">
            </div>
            
            <div class="form-group">
                <label for="test_contenteditable">Contenteditable:</label>
                <div contenteditable="true" name="settings[content_field]" id="test_contenteditable" 
                     style="border: 1px solid #ddd; padding: 8px; min-height: 40px;">
                    Edit this content...
                </div>
            </div>
            
            <div class="form-group">
                <label for="test_data_value">Data-Value Element:</label>
                <div data-value="test-value" name="settings[data_field]" id="test_data_value" 
                     style="border: 1px solid #ddd; padding: 8px; cursor: pointer; background: #f9f9f9;"
                     onclick="toggleDataValue(this)">
                    Click to toggle: test-value
                </div>
            </div>
            
        </form>
    </div>

    <div class="test-container">
        <h3>🔍 Test Controls</h3>
        <button type="button" class="test-button" onclick="testUniversalDetection()">Test Universal Detection</button>
        <button type="button" class="test-button" onclick="testToastSystem()">Test Toast System</button>
        <button type="button" class="test-button" onclick="simulateAutoSave()">Simulate Auto-Save</button>
        <button type="button" class="test-button" onclick="clearLog()">Clear Log</button>
    </div>

    <div class="test-container">
        <h3>📋 Test Results</h3>
        <div id="test-log" class="log-output">Ready to run tests...\n</div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#test-log').append(`[${timestamp}] ${message}\n`);
            $('#test-log').scrollTop($('#test-log')[0].scrollHeight);
        }
        
        function clearLog() {
            $('#test-log').text('Log cleared...\n');
        }
        
        function toggleDataValue(element) {
            const currentValue = element.dataset.value;
            const newValue = currentValue === 'enabled' ? 'disabled' : 'enabled';
            element.dataset.value = newValue;
            element.textContent = `Click to toggle: ${newValue}`;
            $(element).trigger('change');
        }
        
        function testUniversalDetection() {
            log('🔮 Testing Universal Field Detection...');
            
            const $form = $('.redco-module-form');
            let detectedFields = 0;
            
            $form.find('*').each(function() {
                const $element = $(this);
                
                if (isInteractiveFormElement($element)) {
                    detectedFields++;
                    const tagName = $element.prop('tagName').toLowerCase();
                    const name = $element.attr('name') || 'unnamed';
                    const type = $element.attr('type') || 'N/A';
                    const contenteditable = $element.attr('contenteditable') || 'N/A';
                    const dataValue = $element.data('value') || 'N/A';
                    
                    log(`  ✅ Detected: ${name} (${tagName}, type=${type}, contenteditable=${contenteditable}, data-value=${dataValue})`);
                }
            });
            
            log(`📊 Detection Results: ${detectedFields} interactive fields found`);
            
            if (detectedFields > 0) {
                log('✅ Universal detection test PASSED');
            } else {
                log('❌ Universal detection test FAILED');
            }
        }
        
        function isInteractiveFormElement($element) {
            const tagName = $element.prop('tagName').toLowerCase();

            // Standard form elements
            if (tagName === 'input' || tagName === 'select' || tagName === 'textarea') {
                return true;
            }

            // Elements with contenteditable
            if ($element.attr('contenteditable') === 'true') {
                return true;
            }

            // Elements with data attributes indicating they're form controls
            if ($element.data('field') || $element.data('setting') || $element.data('value')) {
                return true;
            }

            // Threshold preset buttons (Lazy Load module)
            if ($element.hasClass('threshold-preset')) {
                return true;
            }

            // Threshold sliders (even without name attribute)
            if ($element.hasClass('threshold-slider') || $element.attr('id') === 'threshold-slider') {
                return true;
            }

            // Elements that have a name attribute
            if ($element.attr('name')) {
                return true;
            }

            return false;
        }
        
        function testToastSystem() {
            log('🍞 Testing Single Persistent Toast System...');

            // Test the single updating toast
            showPersistentToast('Saving changes...', 'info');
            log('  🔵 Showing initial "Saving..." state');

            setTimeout(() => {
                updatePersistentToast('Settings saved successfully!', 'success');
                log('  🟢 Updated to "Saved" state');
            }, 2000);

            setTimeout(() => {
                updatePersistentToast('Save queued for processing', 'warning');
                log('  🟨 Updated to "Queued" state');
            }, 4000);

            setTimeout(() => {
                updatePersistentToast('Save failed, please try again', 'error');
                log('  🔴 Updated to "Error" state');
            }, 6000);

            setTimeout(() => {
                hidePersistentToast();
                log('  ✅ Toast hidden after sequence');
            }, 10000);

            log('✅ Single persistent toast test initiated - watch top-right corner for updates');
        }
        
        // Single persistent toast system
        function showPersistentToast(message, type) {
            // Create toast container if it doesn't exist
            if ($('.redco-toast-container').length === 0) {
                $('body').append('<div class="redco-toast-container"></div>');
            }

            const $container = $('.redco-toast-container');

            // Check if persistent toast already exists
            let $toast = $container.find('#redco-persistent-toast');

            if ($toast.length === 0) {
                // Create the persistent toast
                $toast = $(`
                    <div class="redco-toast redco-toast-${type}" id="redco-persistent-toast">
                        <div class="toast-content">
                            <span class="toast-icon"></span>
                            <span class="toast-message">${message}</span>
                            <button class="toast-close" onclick="hidePersistentToast()">&times;</button>
                        </div>
                    </div>
                `);

                // Add toast to container
                $container.append($toast);

                // Show immediately (no delay)
                $toast.addClass('show');

            } else {
                // Update existing toast
                updatePersistentToast(message, type);
            }
        }

        function updatePersistentToast(message, type) {
            const $toast = $('#redco-persistent-toast');
            if ($toast.length === 0) {
                showPersistentToast(message, type);
                return;
            }

            // Remove existing type classes
            $toast.removeClass('redco-toast-info redco-toast-success redco-toast-warning redco-toast-error');

            // Add new type class
            $toast.addClass('redco-toast-' + type);

            // Update message
            $toast.find('.toast-message').text(message);

            // Add update animation
            $toast.addClass('updating');
            setTimeout(() => {
                $toast.removeClass('updating');
            }, 200);
        }

        function hidePersistentToast() {
            const $toast = $('#redco-persistent-toast');
            if ($toast.length > 0) {
                $toast.removeClass('show');
                setTimeout(() => {
                    $toast.remove();
                }, 300);
            }
        }
        
        function closeToast(toastId) {
            const $toast = $('#' + toastId);
            $toast.removeClass('show');
            setTimeout(() => {
                $toast.remove();
            }, 300);
        }
        
        function simulateAutoSave() {
            log('💾 Simulating Realistic Auto-Save Process...');

            // Show initial saving state immediately
            showPersistentToast('Saving changes...', 'info');
            log('  🔵 Immediate: "Saving changes..." toast shown');

            // Simulate save completion after realistic delay
            setTimeout(() => {
                updatePersistentToast('Settings saved successfully', 'success');
                log('  🟢 Updated: "Settings saved successfully"');

                // Auto-hide after success
                setTimeout(() => {
                    hidePersistentToast();
                    log('  ✅ Auto-save simulation completed - toast hidden');
                }, 3000);
            }, 1500);
        }
        
        $(document).ready(function() {
            log('🚀 Universal Auto-Save Test Page Loaded');
            log('💡 Use the test buttons to verify the system');
            log('');
            
            // Auto-run detection test
            setTimeout(() => {
                testUniversalDetection();
            }, 500);
        });
    </script>
</body>
</html>
