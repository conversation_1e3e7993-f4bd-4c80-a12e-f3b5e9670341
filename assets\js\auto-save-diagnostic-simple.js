/**
 * Simple Auto-Save Diagnostic for Asset Optimization
 * 
 * Quick diagnostic to identify auto-save issues
 */

(function($) {
    'use strict';

    window.RedcoAutoSaveDiagnostic = {
        
        // Run diagnostic
        run: function() {
            console.log('🔧 Running Auto-Save Diagnostic...');
            
            this.checkFormDetection();
            this.checkStatusIndicators();
            this.checkMemoryLogic();
            this.testSampleSave();
        },

        // Check if forms are being detected
        checkFormDetection: function() {
            console.log('\n📋 FORM DETECTION CHECK:');
            
            const forms = $('.redco-module-form, form[data-module], .redco-settings-form, form.redco-form');
            console.log(`Found ${forms.length} auto-save forms`);
            
            forms.each(function(index) {
                const $form = $(this);
                const module = $form.data('module');
                const classes = $form.attr('class');
                const id = $form.attr('id');
                
                console.log(`Form ${index + 1}:`, {
                    module: module,
                    classes: classes,
                    id: id,
                    fieldCount: $form.find('input, select, textarea').length
                });
            });
            
            // Check specifically for asset-optimization
            const assetOptForm = $('form[data-module="asset-optimization"], .redco-module-form[data-module="asset-optimization"]');
            console.log(`Asset Optimization forms found: ${assetOptForm.length}`);
            
            if (assetOptForm.length > 0) {
                console.log('✅ Asset Optimization form detected');
                
                const fields = assetOptForm.find('input, select, textarea');
                console.log(`Fields in Asset Optimization form: ${fields.length}`);
                
                fields.each(function(index) {
                    const $field = $(this);
                    const name = $field.attr('name');
                    const type = $field.attr('type') || $field.prop('tagName').toLowerCase();
                    const hasAutoSave = $field.hasClass('redco-auto-save-enabled');
                    const hasIndicator = $field.siblings('.redco-auto-save-status').length > 0;
                    
                    if (index < 5) { // Show first 5 fields
                        console.log(`  Field ${index + 1}: ${name} (${type}) - Auto-save: ${hasAutoSave}, Indicator: ${hasIndicator}`);
                    }
                });
            } else {
                console.log('❌ Asset Optimization form NOT detected');
                
                // Check for any forms that might be asset optimization
                $('form').each(function() {
                    const $form = $(this);
                    const action = $form.attr('action') || '';
                    const classes = $form.attr('class') || '';
                    const id = $form.attr('id') || '';
                    
                    if (action.includes('asset') || classes.includes('asset') || id.includes('asset') ||
                        window.location.href.includes('asset-optimization')) {
                        console.log('🔍 Potential Asset Optimization form found:', {
                            action: action,
                            classes: classes,
                            id: id
                        });
                    }
                });
            }
        },

        // Check status indicators
        checkStatusIndicators: function() {
            console.log('\n🎯 STATUS INDICATOR CHECK:');
            
            const indicators = $('.redco-auto-save-status');
            console.log(`Found ${indicators.length} status indicators`);
            
            const enabledFields = $('.redco-auto-save-enabled');
            console.log(`Found ${enabledFields.length} auto-save enabled fields`);
            
            // Check CSS loading
            const cssLoaded = $('link[href*="auto-save-indicators.css"]').length > 0;
            console.log(`Auto-save CSS loaded: ${cssLoaded}`);
            
            if (!cssLoaded) {
                console.log('❌ Auto-save CSS not detected - checking for inline styles...');
                const hasInlineStyles = $('.redco-auto-save-status').length > 0 && 
                                       $('.redco-auto-save-status').css('display') !== 'none';
                console.log(`Inline styles working: ${hasInlineStyles}`);
            }
        },

        // Check memory logic
        checkMemoryLogic: function() {
            console.log('\n🧠 MEMORY LOGIC CHECK:');
            
            // Test field classification
            const testFields = [
                'settings[minify_css]',
                'settings[minify_js]', 
                'settings[critical_css]',
                'settings[enabled]',
                'settings[defer_non_critical]'
            ];
            
            testFields.forEach(fieldName => {
                // Simulate the PHP logic in JavaScript
                const fieldKey = fieldName.replace(/^settings\[([^\]]+)\]$/, '$1');
                const isMemoryIntensive = ['minify_css', 'minify_js', 'critical_css'].includes(fieldKey);
                
                console.log(`${fieldName} -> ${fieldKey} -> Memory intensive: ${isMemoryIntensive}`);
            });
        },

        // Test a sample save
        testSampleSave: function() {
            console.log('\n🧪 SAMPLE SAVE TEST:');
            
            const testField = $('input[name*="settings["], select[name*="settings["], textarea[name*="settings["]').first();
            
            if (testField.length > 0) {
                const fieldName = testField.attr('name');
                const currentValue = testField.val();
                
                console.log(`Testing save for field: ${fieldName}`);
                console.log(`Current value: ${currentValue}`);
                
                // Simulate auto-save request
                const saveData = {
                    action: 'redco_global_auto_save',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: 'asset-optimization',
                    field_name: fieldName,
                    field_value: currentValue
                };
                
                console.log('Save data:', saveData);
                
                // Don't actually send the request, just log what would be sent
                console.log('💡 To test actual save, run: RedcoAutoSaveDiagnostic.performTestSave()');
            } else {
                console.log('❌ No test fields found');
            }
        },

        // Perform actual test save
        performTestSave: function() {
            const testField = $('input[name*="settings["], select[name*="settings["], textarea[name*="settings["]').first();
            
            if (testField.length === 0) {
                console.log('❌ No test field available');
                return;
            }
            
            const fieldName = testField.attr('name');
            const currentValue = testField.val();
            
            console.log(`🚀 Performing test save for: ${fieldName}`);
            
            $.ajax({
                url: redcoAjax.ajaxurl,
                type: 'POST',
                data: {
                    action: 'redco_global_auto_save',
                    nonce: redcoAjax.global_auto_save_nonce || redcoAjax.nonce,
                    module: 'asset-optimization',
                    field_name: fieldName,
                    field_value: currentValue
                },
                success: function(response) {
                    console.log('✅ Save response:', response);
                    
                    if (response.success) {
                        if (response.data.queued) {
                            console.log('📋 Save was QUEUED (this might be the issue!)');
                        } else {
                            console.log('✅ Save completed DIRECTLY');
                        }
                    } else {
                        console.log('❌ Save failed:', response.data);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('❌ Save error:', error);
                }
            });
        },

        // Fix status indicators
        fixStatusIndicators: function() {
            console.log('🔧 Attempting to fix status indicators...');
            
            const assetOptForm = $('form[data-module="asset-optimization"], .redco-module-form[data-module="asset-optimization"]');
            
            if (assetOptForm.length === 0) {
                console.log('❌ No Asset Optimization form found to fix');
                return;
            }
            
            let fixed = 0;
            
            assetOptForm.find('input, select, textarea').each(function() {
                const $field = $(this);
                const fieldName = $field.attr('name');
                
                if (fieldName && !$field.hasClass('redco-auto-save-enabled')) {
                    // Add auto-save class
                    $field.addClass('redco-auto-save-enabled');
                    
                    // Add status indicator if missing
                    if ($field.siblings('.redco-auto-save-status').length === 0) {
                        const $indicator = $('<span class="redco-auto-save-status" role="status" aria-live="polite">' +
                            '<span class="status-icon" aria-hidden="true"></span>' +
                            '<span class="status-text"></span>' +
                            '<span class="sr-only"></span>' +
                            '</span>');
                        $field.after($indicator);
                        fixed++;
                    }
                }
            });
            
            console.log(`✅ Fixed ${fixed} status indicators`);
            
            // Test one indicator
            const testField = assetOptForm.find('input, select, textarea').first();
            if (testField.length > 0) {
                const $indicator = testField.siblings('.redco-auto-save-status');
                
                $indicator.removeClass('saving saved error')
                          .addClass('saved')
                          .find('.status-text').text('Test - Fixed!');
                
                setTimeout(() => {
                    $indicator.removeClass('saved')
                              .find('.status-text').text('');
                }, 3000);
                
                console.log('✅ Test indicator should show "Test - Fixed!" for 3 seconds');
            }
        }
    };

    // Auto-run diagnostic on Asset Optimization pages
    $(document).ready(function() {
        if (window.location.href.includes('asset-optimization') || 
            $('form[data-module="asset-optimization"]').length > 0) {
            
            setTimeout(() => {
                console.log('🔧 Auto-running Asset Optimization diagnostic...');
                RedcoAutoSaveDiagnostic.run();
                
                console.log('\n💡 Available commands:');
                console.log('- RedcoAutoSaveDiagnostic.run() - Run full diagnostic');
                console.log('- RedcoAutoSaveDiagnostic.performTestSave() - Test actual save');
                console.log('- RedcoAutoSaveDiagnostic.fixStatusIndicators() - Fix missing indicators');
            }, 2000);
        }
    });

})(jQuery);

console.log('🔧 Auto-Save Diagnostic Tool loaded');
console.log('💡 Use RedcoAutoSaveDiagnostic.run() to diagnose auto-save issues');
