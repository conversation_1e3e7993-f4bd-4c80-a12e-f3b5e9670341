/**
 * Auto-Save Test Helper
 * 
 * Creates test forms and provides utilities for testing auto-save functionality
 * when not on a module page
 */

(function($) {
    'use strict';

    window.RedcoAutoSaveTestHelper = {
        
        // Create a test form for auto-save testing
        createTestForm: function(module = 'test-module') {
            console.log(`🧪 Creating test form for module: ${module}`);
            
            // Remove existing test form
            $('#redco-test-form-container').remove();
            
            const testFormHtml = `
                <div id="redco-test-form-container" style="position: fixed; top: 100px; left: 20px; background: white; border: 2px solid #007cba; padding: 20px; z-index: 10000; width: 400px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                    <h4>🧪 Auto-Save Test Form</h4>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">
                        This form simulates a Redco Optimizer module form for testing auto-save functionality.
                    </p>
                    
                    <form id="redco-test-form" class="redco-module-form" data-module="${module}">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="test-text-field" style="display: block; margin-bottom: 5px; font-weight: bold;">Text Field:</label>
                            <input type="text" id="test-text-field" name="settings[test_text]" value="initial value" style="width: 100%; padding: 5px; border: 1px solid #ccc;" />
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                                <input type="checkbox" id="test-checkbox" name="settings[test_checkbox]" value="1" style="margin-right: 5px;" />
                                Enable Test Feature
                            </label>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="test-select" style="display: block; margin-bottom: 5px; font-weight: bold;">Select Option:</label>
                            <select id="test-select" name="settings[test_select]" style="width: 100%; padding: 5px; border: 1px solid #ccc;">
                                <option value="option1">Option 1</option>
                                <option value="option2">Option 2</option>
                                <option value="option3">Option 3</option>
                            </select>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="test-range" style="display: block; margin-bottom: 5px; font-weight: bold;">Range Slider:</label>
                            <input type="range" id="test-range" name="settings[test_range]" min="0" max="100" value="50" style="width: 100%;" />
                            <span id="range-value" style="font-size: 12px; color: #666;">50</span>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="test-textarea" style="display: block; margin-bottom: 5px; font-weight: bold;">Textarea:</label>
                            <textarea id="test-textarea" name="settings[test_textarea]" rows="3" style="width: 100%; padding: 5px; border: 1px solid #ccc;">Test content</textarea>
                        </div>
                        
                        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
                            <button type="button" id="trigger-auto-save-test" style="background: #007cba; color: white; border: none; padding: 8px 15px; margin-right: 10px; cursor: pointer; border-radius: 3px;">Test Auto-Save</button>
                            <button type="button" id="run-integration-test" style="background: #28a745; color: white; border: none; padding: 8px 15px; margin-right: 10px; cursor: pointer; border-radius: 3px;">Run Integration Test</button>
                            <button type="button" id="close-test-form" style="background: #dc3545; color: white; border: none; padding: 8px 15px; cursor: pointer; border-radius: 3px;">Close</button>
                        </div>
                        
                        <div id="test-form-status" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 3px; font-size: 12px; display: none;">
                            <strong>Status:</strong> <span id="status-text">Ready</span>
                        </div>
                    </form>
                </div>
            `;
            
            $('body').append(testFormHtml);
            
            // Bind event handlers
            this.bindTestFormEvents(module);
            
            // Trigger auto-save system to detect the new form
            if (typeof RedcoGlobalAutoSave !== 'undefined') {
                setTimeout(() => {
                    RedcoGlobalAutoSave.setupFormDetection();
                    this.updateStatus('Test form created and auto-save system notified');
                }, 100);
            }
            
            return $('#redco-test-form');
        },

        // Bind events to test form
        bindTestFormEvents: function(module) {
            const self = this;
            
            // Close button
            $('#close-test-form').on('click', function() {
                $('#redco-test-form-container').remove();
            });
            
            // Test auto-save button
            $('#trigger-auto-save-test').on('click', function() {
                self.triggerAutoSaveTest();
            });
            
            // Run integration test button
            $('#run-integration-test').on('click', function() {
                if (typeof testAutoSaveIntegration === 'function') {
                    testAutoSaveIntegration();
                } else {
                    console.log('❌ Integration test function not available');
                }
            });
            
            // Range slider value display
            $('#test-range').on('input', function() {
                $('#range-value').text($(this).val());
            });
            
            // Monitor form changes for status updates
            $('#redco-test-form input, #redco-test-form select, #redco-test-form textarea').on('change input', function() {
                self.updateStatus('Field changed - auto-save should trigger in 3 seconds');
            });
        },

        // Trigger auto-save test
        triggerAutoSaveTest: function() {
            console.log('🧪 Triggering auto-save test...');
            this.updateStatus('Triggering auto-save test...');
            
            const $textField = $('#test-text-field');
            if ($textField.length > 0) {
                const originalValue = $textField.val();
                const testValue = 'auto-save-test-' + Date.now();
                
                $textField.val(testValue).trigger('change');
                
                this.updateStatus(`Changed text field to: ${testValue}`);
                
                // Restore original value after test
                setTimeout(() => {
                    $textField.val(originalValue);
                    this.updateStatus('Test completed, original value restored');
                }, 10000);
            } else {
                this.updateStatus('❌ Test field not found');
            }
        },

        // Update status display
        updateStatus: function(message) {
            const $statusDiv = $('#test-form-status');
            const $statusText = $('#status-text');
            
            if ($statusDiv.length > 0 && $statusText.length > 0) {
                $statusText.text(message);
                $statusDiv.show();
                
                console.log(`📊 Test Status: ${message}`);
            }
        },

        // Create asset optimization test form specifically
        createAssetOptimizationTestForm: function() {
            console.log('🎯 Creating Asset Optimization test form...');
            
            // Remove existing test form
            $('#redco-test-form-container').remove();
            
            const testFormHtml = `
                <div id="redco-test-form-container" style="position: fixed; top: 100px; left: 20px; background: white; border: 2px solid #ff6b35; padding: 20px; z-index: 10000; width: 450px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                    <h4>🎯 Asset Optimization Test Form</h4>
                    <p style="font-size: 12px; color: #666; margin-bottom: 15px;">
                        This form simulates the asset-optimization module that was causing memory issues.
                    </p>
                    
                    <form id="redco-test-form" class="redco-module-form" data-module="asset-optimization">
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                                <input type="checkbox" id="minify-css" name="settings[minify_css]" value="1" style="margin-right: 5px;" />
                                Minify CSS (Memory Intensive)
                            </label>
                            <small style="color: #666;">This setting was causing memory limit exceeded errors</small>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                                <input type="checkbox" id="minify-js" name="settings[minify_js]" value="1" style="margin-right: 5px;" />
                                Minify JavaScript (Memory Intensive)
                            </label>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">
                                <input type="checkbox" id="combine-css" name="settings[combine_css]" value="1" style="margin-right: 5px;" />
                                Combine CSS Files
                            </label>
                        </div>
                        
                        <div class="form-group" style="margin-bottom: 15px;">
                            <label for="optimization-level" style="display: block; margin-bottom: 5px; font-weight: bold;">Optimization Level:</label>
                            <select id="optimization-level" name="settings[optimization_level]" style="width: 100%; padding: 5px; border: 1px solid #ccc;">
                                <option value="basic">Basic</option>
                                <option value="advanced">Advanced</option>
                                <option value="aggressive">Aggressive (High Memory)</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #eee;">
                            <button type="button" id="test-memory-scenario" style="background: #ff6b35; color: white; border: none; padding: 8px 15px; margin-right: 10px; cursor: pointer; border-radius: 3px;">Test Memory Scenario</button>
                            <button type="button" id="run-asset-memory-test" style="background: #28a745; color: white; border: none; padding: 8px 15px; margin-right: 10px; cursor: pointer; border-radius: 3px;">Run Memory Test</button>
                            <button type="button" id="close-test-form" style="background: #dc3545; color: white; border: none; padding: 8px 15px; cursor: pointer; border-radius: 3px;">Close</button>
                        </div>
                        
                        <div id="test-form-status" style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px; font-size: 12px;">
                            <strong>Memory Test Status:</strong> <span id="status-text">Ready to test memory management</span>
                        </div>
                    </form>
                </div>
            `;
            
            $('body').append(testFormHtml);
            
            // Bind specific events for asset optimization testing
            this.bindAssetOptimizationEvents();
            
            // Trigger auto-save system to detect the new form
            if (typeof RedcoGlobalAutoSave !== 'undefined') {
                setTimeout(() => {
                    RedcoGlobalAutoSave.setupFormDetection();
                    this.updateStatus('Asset optimization test form ready');
                }, 100);
            }
        },

        // Bind events for asset optimization testing
        bindAssetOptimizationEvents: function() {
            const self = this;
            
            // Close button
            $('#close-test-form').on('click', function() {
                $('#redco-test-form-container').remove();
            });
            
            // Test memory scenario button
            $('#test-memory-scenario').on('click', function() {
                self.testMemoryScenario();
            });
            
            // Run asset memory test button
            $('#run-asset-memory-test').on('click', function() {
                if (typeof testAssetOptimizationMemory === 'function') {
                    testAssetOptimizationMemory();
                } else {
                    console.log('❌ Asset optimization memory test function not available');
                }
            });
        },

        // Test the specific memory scenario
        testMemoryScenario: function() {
            console.log('🧠 Testing memory scenario...');
            this.updateStatus('Testing memory scenario - toggling minify_css setting...');
            
            const $minifyCss = $('#minify-css');
            if ($minifyCss.length > 0) {
                // Toggle the checkbox to trigger auto-save
                $minifyCss.prop('checked', !$minifyCss.prop('checked')).trigger('change');
                
                this.updateStatus('Minify CSS toggled - auto-save should handle memory constraints gracefully');
            }
        }
    };

    // Auto-create test form if not on a module page
    $(document).ready(function() {
        const isRedcoPage = window.location.href.includes('redco-optimizer') || 
                           $('.redco-optimizer-admin').length > 0;
        
        // Only auto-create if we're on a Redco page but no forms are detected
        if (isRedcoPage) {
            setTimeout(() => {
                const $existingForms = $('.redco-module-form, form[data-module]');
                if ($existingForms.length === 0) {
                    console.log('ℹ️ No module forms detected. Creating test form for auto-save testing.');
                    console.log('💡 Use RedcoAutoSaveTestHelper.createTestForm() to create a test form');
                    console.log('💡 Use RedcoAutoSaveTestHelper.createAssetOptimizationTestForm() for memory testing');
                }
            }, 2000);
        }
    });

    // Add console helpers
    window.createAutoSaveTestForm = function(module = 'test-module') {
        return RedcoAutoSaveTestHelper.createTestForm(module);
    };

    window.createAssetOptimizationTestForm = function() {
        return RedcoAutoSaveTestHelper.createAssetOptimizationTestForm();
    };

})(jQuery);

console.log('🧪 Auto-Save Test Helper loaded');
console.log('💡 Use createAutoSaveTestForm() to create a test form');
console.log('💡 Use createAssetOptimizationTestForm() for memory testing');
