<?php
/**
 * Memory Profiler for Redco Optimizer
 * 
 * Provides detailed memory usage analysis and optimization recommendations
 */

if (!defined('ABSPATH')) {
    exit;
}

class Redco_Memory_Profiler {
    
    private static $memory_snapshots = array();
    private static $operation_profiles = array();
    private static $peak_memory_usage = 0;
    private static $memory_warnings = array();
    
    /**
     * Initialize memory profiler
     */
    public static function init() {
        if (!self::should_profile()) {
            return;
        }
        
        // Take initial memory snapshot
        self::snapshot('init');
        
        // Hook into key operations
        add_action('redco_before_auto_save', array(__CLASS__, 'profile_auto_save_start'));
        add_action('redco_after_auto_save', array(__CLASS__, 'profile_auto_save_end'));
        
        // Monitor memory during asset optimization
        add_action('redco_before_asset_optimization', array(__CLASS__, 'start_profiling'));
        add_action('redco_after_asset_optimization', array(__CLASS__, 'end_profiling'));
        
        // Log memory data on shutdown
        add_action('shutdown', array(__CLASS__, 'log_memory_summary'));
    }
    
    /**
     * Take memory snapshot
     */
    public static function snapshot($label) {
        $memory_data = array(
            'timestamp' => microtime(true),
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true),
            'memory_limit' => wp_convert_hr_to_bytes(ini_get('memory_limit')),
            'label' => $label
        );
        
        $memory_data['usage_percentage'] = ($memory_data['memory_usage'] / $memory_data['memory_limit']) * 100;
        $memory_data['peak_percentage'] = ($memory_data['memory_peak'] / $memory_data['memory_limit']) * 100;
        
        self::$memory_snapshots[$label] = $memory_data;
        
        // Track peak usage
        if ($memory_data['memory_usage'] > self::$peak_memory_usage) {
            self::$peak_memory_usage = $memory_data['memory_usage'];
        }
        
        // Check for memory warnings
        if ($memory_data['usage_percentage'] > 80) {
            self::$memory_warnings[] = array(
                'timestamp' => $memory_data['timestamp'],
                'label' => $label,
                'usage_percentage' => $memory_data['usage_percentage'],
                'memory_usage' => $memory_data['memory_usage']
            );
        }
        
        return $memory_data;
    }
    
    /**
     * Start profiling an operation
     */
    public static function start_profiling($operation = 'unknown') {
        self::snapshot($operation . '_start');
        
        self::$operation_profiles[$operation] = array(
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'operation' => $operation
        );
    }
    
    /**
     * End profiling an operation
     */
    public static function end_profiling($operation = 'unknown') {
        $end_snapshot = self::snapshot($operation . '_end');
        
        if (isset(self::$operation_profiles[$operation])) {
            $profile = &self::$operation_profiles[$operation];
            $profile['end_time'] = microtime(true);
            $profile['end_memory'] = memory_get_usage(true);
            $profile['execution_time'] = $profile['end_time'] - $profile['start_time'];
            $profile['memory_used'] = $profile['end_memory'] - $profile['start_memory'];
            $profile['memory_efficiency'] = $profile['memory_used'] / max($profile['execution_time'], 0.001); // MB per second
        }
    }
    
    /**
     * Profile auto-save start
     */
    public static function profile_auto_save_start($data) {
        $operation = 'auto_save_' . ($data['module'] ?? 'unknown');
        self::start_profiling($operation);
    }
    
    /**
     * Profile auto-save end
     */
    public static function profile_auto_save_end($data) {
        $operation = 'auto_save_' . ($data['module'] ?? 'unknown');
        self::end_profiling($operation);
    }
    
    /**
     * Get memory usage analysis
     */
    public static function get_memory_analysis() {
        $analysis = array(
            'snapshots' => self::$memory_snapshots,
            'operations' => self::$operation_profiles,
            'peak_usage' => self::$peak_memory_usage,
            'warnings' => self::$memory_warnings,
            'recommendations' => self::get_optimization_recommendations()
        );
        
        return $analysis;
    }
    
    /**
     * Get optimization recommendations based on memory usage patterns
     */
    private static function get_optimization_recommendations() {
        $recommendations = array();
        
        // Analyze memory usage patterns
        foreach (self::$operation_profiles as $operation => $profile) {
            if (isset($profile['memory_used']) && $profile['memory_used'] > 10 * 1024 * 1024) { // 10MB
                $recommendations[] = array(
                    'type' => 'high_memory_usage',
                    'operation' => $operation,
                    'memory_used' => $profile['memory_used'],
                    'recommendation' => 'Consider implementing chunked processing for this operation'
                );
            }
            
            if (isset($profile['memory_efficiency']) && $profile['memory_efficiency'] > 5 * 1024 * 1024) { // 5MB/sec
                $recommendations[] = array(
                    'type' => 'memory_efficiency',
                    'operation' => $operation,
                    'efficiency' => $profile['memory_efficiency'],
                    'recommendation' => 'Operation is using memory very quickly - consider streaming processing'
                );
            }
        }
        
        // Check for memory warnings
        if (count(self::$memory_warnings) > 0) {
            $recommendations[] = array(
                'type' => 'memory_warnings',
                'count' => count(self::$memory_warnings),
                'recommendation' => 'Multiple memory warnings detected - consider increasing memory limit or optimizing operations'
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Log memory summary
     */
    public static function log_memory_summary() {
        if (!self::should_profile()) {
            return;
        }
        
        $analysis = self::get_memory_analysis();
        
        // Log summary
        error_log('Redco Memory Profile Summary:');
        error_log('Peak Memory Usage: ' . size_format($analysis['peak_usage']));
        error_log('Memory Warnings: ' . count($analysis['warnings']));
        error_log('Operations Profiled: ' . count($analysis['operations']));
        
        // Log high-memory operations
        foreach ($analysis['operations'] as $operation => $profile) {
            if (isset($profile['memory_used']) && $profile['memory_used'] > 5 * 1024 * 1024) { // 5MB
                error_log("High Memory Operation: {$operation} used " . size_format($profile['memory_used']) . 
                         " in {$profile['execution_time']}s");
            }
        }
        
        // Log recommendations
        foreach ($analysis['recommendations'] as $recommendation) {
            error_log("Memory Recommendation: {$recommendation['recommendation']} (Type: {$recommendation['type']})");
        }
    }
    
    /**
     * Get current memory status
     */
    public static function get_current_memory_status() {
        $current_usage = memory_get_usage(true);
        $peak_usage = memory_get_peak_usage(true);
        $memory_limit = wp_convert_hr_to_bytes(ini_get('memory_limit'));
        
        return array(
            'current_usage' => $current_usage,
            'current_usage_formatted' => size_format($current_usage),
            'peak_usage' => $peak_usage,
            'peak_usage_formatted' => size_format($peak_usage),
            'memory_limit' => $memory_limit,
            'memory_limit_formatted' => size_format($memory_limit),
            'usage_percentage' => ($current_usage / $memory_limit) * 100,
            'peak_percentage' => ($peak_usage / $memory_limit) * 100,
            'available_memory' => $memory_limit - $current_usage,
            'available_memory_formatted' => size_format($memory_limit - $current_usage)
        );
    }
    
    /**
     * Check if memory profiling should be enabled
     */
    private static function should_profile() {
        // Enable in development environment
        if (defined('WP_DEBUG') && WP_DEBUG) {
            return true;
        }
        
        // Enable if explicitly requested
        if (defined('REDCO_ENABLE_MEMORY_PROFILING') && REDCO_ENABLE_MEMORY_PROFILING) {
            return true;
        }
        
        // Enable for memory debugging
        if (isset($_GET['redco_memory_debug']) && current_user_can('manage_options')) {
            return true;
        }
        
        return false;
    }
    
    /**
     * Profile a specific function call
     */
    public static function profile_function($callback, $operation_name = 'function_call') {
        self::start_profiling($operation_name);
        
        try {
            $result = call_user_func($callback);
        } finally {
            self::end_profiling($operation_name);
        }
        
        return $result;
    }
    
    /**
     * Get memory usage for a specific operation
     */
    public static function get_operation_memory_usage($operation) {
        if (isset(self::$operation_profiles[$operation])) {
            return self::$operation_profiles[$operation];
        }
        
        return null;
    }
    
    /**
     * Clear profiling data
     */
    public static function clear_profiling_data() {
        self::$memory_snapshots = array();
        self::$operation_profiles = array();
        self::$peak_memory_usage = 0;
        self::$memory_warnings = array();
    }
    
    /**
     * Export profiling data for analysis
     */
    public static function export_profiling_data() {
        return array(
            'timestamp' => current_time('mysql'),
            'memory_limit' => wp_convert_hr_to_bytes(ini_get('memory_limit')),
            'php_version' => PHP_VERSION,
            'wordpress_version' => get_bloginfo('version'),
            'analysis' => self::get_memory_analysis()
        );
    }
}

// Initialize memory profiler
add_action('init', array('Redco_Memory_Profiler', 'init'), 1);
