/**
 * Auto-Save Debug Script
 * 
 * Comprehensive debugging for auto-save functionality and loading indicator
 */

(function($) {
    'use strict';

    const AutoSaveDebug = {
        init: function() {
            this.addDebugPanel();
            this.runDiagnostics();
            this.bindTestEvents();
        },

        addDebugPanel: function() {
            const debugPanel = `
                <div id="auto-save-debug-panel" style="position: fixed; bottom: 20px; right: 20px; background: #fff; border: 2px solid #0073aa; border-radius: 8px; padding: 15px; z-index: 999999; max-width: 400px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <h4 style="margin: 0 0 10px 0; color: #0073aa;">🔧 Auto-Save Debug Panel</h4>
                    <div id="debug-status" style="margin-bottom: 10px; font-size: 12px;"></div>
                    <div id="debug-tests" style="margin-bottom: 10px;">
                        <button id="test-loading-indicator" style="margin: 2px; padding: 4px 8px; font-size: 11px;">Test Loading</button>
                        <button id="test-auto-save" style="margin: 2px; padding: 4px 8px; font-size: 11px;">Test Auto-Save</button>
                        <button id="test-form-detection" style="margin: 2px; padding: 4px 8px; font-size: 11px;">Test Forms</button>
                        <button id="run-diagnostics" style="margin: 2px; padding: 4px 8px; font-size: 11px;">Run Diagnostics</button>
                    </div>
                    <div id="debug-log" style="max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 10px;"></div>
                    <button id="close-debug-panel" style="position: absolute; top: 5px; right: 8px; background: none; border: none; font-size: 16px; cursor: pointer;">×</button>
                </div>
            `;
            
            $('body').append(debugPanel);
            
            $('#close-debug-panel').on('click', function() {
                $('#auto-save-debug-panel').remove();
            });
        },

        log: function(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#333',
                success: '#46b450',
                error: '#dc3545',
                warning: '#f56e28'
            };
            
            const logEntry = `<div style="color: ${colors[type]}; margin: 2px 0;">[${timestamp}] ${message}</div>`;
            $('#debug-log').append(logEntry);
            $('#debug-log').scrollTop($('#debug-log')[0].scrollHeight);
        },

        runDiagnostics: function() {
            this.log('🔍 Running Auto-Save Diagnostics...', 'info');
            
            // Check if RedcoAutoSave exists
            if (typeof RedcoAutoSave !== 'undefined') {
                this.log('✅ RedcoAutoSave object found', 'success');
                
                // Check if it has required methods
                const requiredMethods = ['init', 'showLoadingIndicator', 'hideLoadingIndicator', 'bindEvents'];
                requiredMethods.forEach(method => {
                    if (typeof RedcoAutoSave[method] === 'function') {
                        this.log(`✅ Method ${method} exists`, 'success');
                    } else {
                        this.log(`❌ Method ${method} missing`, 'error');
                    }
                });
            } else {
                this.log('❌ RedcoAutoSave object not found', 'error');
            }
            
            // Check for loading indicator in DOM
            const $indicator = $('#redco-auto-save-loading');
            if ($indicator.length > 0) {
                this.log('✅ Loading indicator element found in DOM', 'success');
                this.log(`📍 Indicator position: ${$indicator.css('position')}`, 'info');
                this.log(`👁️ Indicator visibility: ${$indicator.is(':visible') ? 'visible' : 'hidden'}`, 'info');
            } else {
                this.log('❌ Loading indicator element not found in DOM', 'error');
            }
            
            // Check for CSS styles
            if ($('#redco-loading-styles').length > 0) {
                this.log('✅ Loading indicator styles found', 'success');
            } else {
                this.log('⚠️ Loading indicator styles not found', 'warning');
            }
            
            // Check for module forms
            const $forms = $('.redco-module-form');
            this.log(`📋 Found ${$forms.length} module forms`, 'info');
            
            $forms.each((index, form) => {
                const $form = $(form);
                const module = $form.data('module');
                if (module) {
                    this.log(`📋 Form ${index + 1}: module="${module}"`, 'info');
                } else {
                    this.log(`⚠️ Form ${index + 1}: missing data-module attribute`, 'warning');
                }
            });
            
            // Check for AJAX configuration
            if (typeof redcoAjax !== 'undefined') {
                this.log('✅ AJAX configuration found', 'success');
                this.log(`🔗 AJAX URL: ${redcoAjax.ajaxurl}`, 'info');
            } else {
                this.log('❌ AJAX configuration missing', 'error');
            }
        },

        bindTestEvents: function() {
            const self = this;
            
            $('#test-loading-indicator').on('click', function() {
                self.log('🧪 Testing loading indicator...', 'info');
                
                if (typeof RedcoAutoSave !== 'undefined' && RedcoAutoSave.showLoadingIndicator) {
                    RedcoAutoSave.showLoadingIndicator('test-module');
                    self.log('✅ Loading indicator shown', 'success');
                    
                    setTimeout(() => {
                        RedcoAutoSave.hideLoadingIndicator('success', 'Test completed!');
                        self.log('✅ Loading indicator hidden with success state', 'success');
                    }, 3000);
                } else {
                    self.log('❌ Cannot test loading indicator - RedcoAutoSave not available', 'error');
                }
            });
            
            $('#test-auto-save').on('click', function() {
                self.log('🧪 Testing auto-save trigger...', 'info');
                
                const $firstForm = $('.redco-module-form').first();
                if ($firstForm.length > 0) {
                    const module = $firstForm.data('module');
                    self.log(`📋 Testing with form module: ${module}`, 'info');
                    
                    const $firstInput = $firstForm.find('input, select, textarea').first();
                    if ($firstInput.length > 0) {
                        self.log(`📝 Triggering change on: ${$firstInput.attr('name')}`, 'info');
                        $firstInput.trigger('change');
                        self.log('✅ Change event triggered', 'success');
                    } else {
                        self.log('⚠️ No input fields found in form', 'warning');
                    }
                } else {
                    self.log('❌ No module forms found', 'error');
                }
            });
            
            $('#test-form-detection').on('click', function() {
                self.log('🧪 Testing form detection...', 'info');
                
                $('.redco-module-form').each(function(index) {
                    const $form = $(this);
                    const module = $form.data('module');
                    const inputCount = $form.find('input, select, textarea').length;
                    
                    self.log(`📋 Form ${index + 1}: module="${module}", inputs=${inputCount}`, 'info');
                    
                    if (!module) {
                        self.log(`⚠️ Form ${index + 1} missing data-module attribute`, 'warning');
                    }
                    
                    if (inputCount === 0) {
                        self.log(`⚠️ Form ${index + 1} has no input fields`, 'warning');
                    }
                });
            });
            
            $('#run-diagnostics').on('click', function() {
                $('#debug-log').empty();
                self.runDiagnostics();
            });
        },

        updateStatus: function() {
            const redcoAutoSaveExists = typeof RedcoAutoSave !== 'undefined';
            const indicatorExists = $('#redco-auto-save-loading').length > 0;
            const formsExist = $('.redco-module-form').length > 0;
            
            let status = '';
            if (redcoAutoSaveExists && indicatorExists && formsExist) {
                status = '✅ All systems operational';
            } else {
                status = '⚠️ Issues detected';
            }
            
            $('#debug-status').html(`
                <div><strong>Status:</strong> ${status}</div>
                <div>RedcoAutoSave: ${redcoAutoSaveExists ? '✅' : '❌'}</div>
                <div>Loading Indicator: ${indicatorExists ? '✅' : '❌'}</div>
                <div>Module Forms: ${formsExist ? `✅ (${$('.redco-module-form').length})` : '❌'}</div>
            `);
        }
    };

    // Initialize debug panel when document is ready
    $(document).ready(function() {
        // Only show debug panel on Redco pages
        if ($('.redco-module-form, .redco-optimizer-admin').length > 0) {
            setTimeout(() => {
                AutoSaveDebug.init();
                AutoSaveDebug.updateStatus();
            }, 2000);
        }
    });

    // Expose to global scope for manual testing
    window.AutoSaveDebug = AutoSaveDebug;

})(jQuery);
