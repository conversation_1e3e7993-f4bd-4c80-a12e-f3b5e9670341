/**
 * Auto-Save Integration Test
 * 
 * Quick verification that the global auto-save system is working
 * with existing module forms
 */

(function($) {
    'use strict';

    window.RedcoAutoSaveIntegrationTest = {
        
        // Test results
        results: {
            systemLoaded: false,
            formsDetected: 0,
            fieldsProcessed: 0,
            indicatorsAdded: 0,
            errors: []
        },

        // Run integration test
        run: function() {
            console.log('🔍 Running Auto-Save Integration Test...');
            
            this.testSystemLoaded();
            this.testFormDetection();
            this.testFieldProcessing();
            this.testStatusIndicators();
            this.testEventBinding();
            this.displayResults();
        },

        // Test if global auto-save system is loaded
        testSystemLoaded: function() {
            if (typeof window.RedcoGlobalAutoSave !== 'undefined') {
                this.results.systemLoaded = true;
                console.log('✅ Global Auto-Save system loaded');
            } else {
                this.results.errors.push('Global Auto-Save system not loaded');
                console.log('❌ Global Auto-Save system not loaded');
            }
        },

        // Test form detection
        testFormDetection: function() {
            // First, let's see what page we're on
            const currentUrl = window.location.href;
            const isRedcoPage = currentUrl.includes('redco-optimizer') || $('.redco-optimizer-admin').length > 0;

            console.log(`🔍 Current page: ${currentUrl}`);
            console.log(`📄 Is Redco page: ${isRedcoPage}`);

            // Look for various form selectors
            const $forms = $('.redco-module-form, form[data-module], .redco-settings-form');
            const $allForms = $('form');
            const $redcoElements = $('.redco-optimizer-admin, .redco-module-tab, .redco-settings-form');

            console.log(`📋 Total forms on page: ${$allForms.length}`);
            console.log(`🎯 Redco-specific forms: ${$forms.length}`);
            console.log(`🏗️ Redco admin elements: ${$redcoElements.length}`);

            this.results.formsDetected = $forms.length;

            if ($forms.length > 0) {
                console.log(`✅ Detected ${$forms.length} forms for auto-save`);

                $forms.each(function() {
                    const $form = $(this);
                    const module = $form.data('module');
                    const classes = $form.attr('class') || '';
                    console.log(`   - Form: ${module || 'unknown module'} (classes: ${classes})`);
                });
            } else {
                // If no forms found, provide helpful debugging info
                if (!isRedcoPage) {
                    console.log('ℹ️ Not on a Redco Optimizer page - auto-save system is designed for module pages');
                    console.log('💡 Navigate to a module page (e.g., Page Cache, Asset Optimization) to test auto-save');
                } else {
                    console.log('⚠️ On Redco page but no forms detected');

                    // Check for any forms that might need the correct classes
                    $allForms.each(function(index) {
                        const $form = $(this);
                        const id = $form.attr('id') || `form-${index}`;
                        const classes = $form.attr('class') || 'no-classes';
                        console.log(`   📝 Form found: ${id} (${classes})`);
                    });
                }

                this.results.errors.push('No forms detected for auto-save');
            }
        },

        // Test field processing
        testFieldProcessing: function() {
            const $fields = $('.redco-module-form input, .redco-module-form select, .redco-module-form textarea');
            this.results.fieldsProcessed = $fields.length;
            
            if ($fields.length > 0) {
                console.log(`✅ Found ${$fields.length} form fields`);
                
                // Check if fields have been processed by auto-save system
                let processedFields = 0;
                $fields.each(function() {
                    const $field = $(this);
                    if ($field.hasClass('redco-auto-save-enabled')) {
                        processedFields++;
                    }
                });
                
                if (processedFields > 0) {
                    console.log(`✅ ${processedFields} fields processed by auto-save system`);
                } else {
                    console.log(`⚠️ Fields found but not yet processed by auto-save system`);
                }
            } else {
                this.results.errors.push('No form fields found');
                console.log('❌ No form fields found');
            }
        },

        // Test status indicators
        testStatusIndicators: function() {
            const $indicators = $('.redco-auto-save-status');
            this.results.indicatorsAdded = $indicators.length;
            
            if ($indicators.length > 0) {
                console.log(`✅ ${$indicators.length} status indicators added`);
            } else {
                console.log('⚠️ No status indicators found (may be added dynamically)');
            }
        },

        // Test event binding
        testEventBinding: function() {
            const $testField = $('.redco-module-form input').first();
            
            if ($testField.length > 0) {
                console.log('✅ Test field found for event testing');
                
                // Check if field has event handlers
                const events = $._data($testField[0], 'events');
                if (events && (events.input || events.change)) {
                    console.log('✅ Event handlers detected on form fields');
                } else {
                    console.log('⚠️ No event handlers detected (may use delegated events)');
                }
            } else {
                this.results.errors.push('No test field available for event testing');
                console.log('❌ No test field available for event testing');
            }
        },

        // Display test results
        displayResults: function() {
            console.log('\n📊 Auto-Save Integration Test Results:');
            console.log(`System Loaded: ${this.results.systemLoaded ? 'Yes' : 'No'}`);
            console.log(`Forms Detected: ${this.results.formsDetected}`);
            console.log(`Fields Processed: ${this.results.fieldsProcessed}`);
            console.log(`Status Indicators: ${this.results.indicatorsAdded}`);
            console.log(`Errors: ${this.results.errors.length}`);
            
            if (this.results.errors.length > 0) {
                console.log('\n❌ Errors found:');
                this.results.errors.forEach(error => {
                    console.log(`   - ${error}`);
                });
            }

            // Show summary
            const isWorking = this.results.systemLoaded && 
                             this.results.formsDetected > 0 && 
                             this.results.fieldsProcessed > 0 &&
                             this.results.errors.length === 0;

            if (isWorking) {
                console.log('\n🎉 Auto-Save Integration Test: PASSED');
                this.showSuccessNotification();
            } else {
                console.log('\n⚠️ Auto-Save Integration Test: NEEDS ATTENTION');
                this.showWarningNotification();
            }
        },

        // Show success notification
        showSuccessNotification: function() {
            if (typeof RedcoGlobalAutoSave !== 'undefined' && RedcoGlobalAutoSave.showGlobalNotification) {
                RedcoGlobalAutoSave.showGlobalNotification(
                    'Auto-Save Integration Test: All systems working correctly!', 
                    'success'
                );
            }
        },

        // Show warning notification
        showWarningNotification: function() {
            if (typeof RedcoGlobalAutoSave !== 'undefined' && RedcoGlobalAutoSave.showGlobalNotification) {
                RedcoGlobalAutoSave.showGlobalNotification(
                    'Auto-Save Integration Test: Some issues detected. Check console for details.', 
                    'warning'
                );
            }
        },

        // Manual test trigger
        triggerManualTest: function() {
            const $testField = $('.redco-module-form input[type="text"], .redco-module-form select').first();
            
            if ($testField.length > 0) {
                console.log('🧪 Triggering manual auto-save test...');
                
                // Change the field value
                const originalValue = $testField.val();
                const testValue = 'auto-save-test-' + Date.now();
                
                $testField.val(testValue).trigger('change');
                
                console.log(`Changed field value from "${originalValue}" to "${testValue}"`);
                console.log('Auto-save should trigger in 3 seconds...');
                
                // Restore original value after test
                setTimeout(() => {
                    $testField.val(originalValue);
                    console.log('Test completed, original value restored');
                }, 10000);
                
            } else {
                console.log('❌ No suitable test field found for manual test');
            }
        }
    };

    // Auto-run test when page loads
    $(document).ready(function() {
        // Wait for auto-save system to initialize
        setTimeout(() => {
            RedcoAutoSaveIntegrationTest.run();
        }, 3000);
    });

    // Add manual test trigger to console
    window.testAutoSave = function() {
        RedcoAutoSaveIntegrationTest.triggerManualTest();
    };

    // Add integration test re-run to console
    window.testAutoSaveIntegration = function() {
        RedcoAutoSaveIntegrationTest.run();
    };

})(jQuery);

// Console helper messages
console.log('🔧 Auto-Save Integration Test loaded');
console.log('💡 Use testAutoSave() to manually trigger an auto-save test');
console.log('💡 Use testAutoSaveIntegration() to re-run the integration test');
