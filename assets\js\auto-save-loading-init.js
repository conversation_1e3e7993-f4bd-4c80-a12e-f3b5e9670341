/**
 * Auto-Save Loading Indicator Initialization
 * 
 * Ensures the loading indicator is properly initialized
 * and handles edge cases for better user experience.
 */

(function($) {
    'use strict';

    // Initialize when document is ready
    $(document).ready(function() {
        // Ensure RedcoAutoSave is available
        if (typeof RedcoAutoSave !== 'undefined') {
            // Initialize auto-save if not already done
            if (typeof RedcoAutoSave.init === 'function') {
                RedcoAutoSave.init();
            }
        }

        // Add global error handler for AJAX requests to ensure loading indicator is hidden
        $(document).ajaxError(function(event, xhr, settings) {
            // Check if this was a Redco auto-save request
            if (settings.data && typeof settings.data === 'string' && 
                settings.data.includes('redco_save_module_settings')) {
                
                // Ensure loading indicator is hidden on AJAX errors
                setTimeout(function() {
                    const $indicator = $('#redco-auto-save-loading');
                    if ($indicator.is(':visible')) {
                        $indicator.removeClass('show success error').addClass('hide');
                        setTimeout(() => {
                            $indicator.fadeOut(200);
                        }, 100);
                    }
                }, 500);
            }
        });

        // Add global success handler to ensure proper cleanup
        $(document).ajaxSuccess(function(event, xhr, settings) {
            // Check if this was a Redco auto-save request
            if (settings.data && typeof settings.data === 'string' && 
                settings.data.includes('redco_save_module_settings')) {
                
                // Ensure loading indicator state is properly managed
                setTimeout(function() {
                    const $indicator = $('#redco-auto-save-loading');
                    if ($indicator.is(':visible') && !$indicator.hasClass('success') && !$indicator.hasClass('error')) {
                        // If indicator is still showing without success/error state, hide it
                        $indicator.removeClass('show').addClass('hide');
                        setTimeout(() => {
                            $indicator.fadeOut(200);
                        }, 100);
                    }
                }, 2000);
            }
        });

        // Handle page visibility changes to clean up loading indicator
        if (typeof document.hidden !== 'undefined') {
            document.addEventListener('visibilitychange', function() {
                if (document.hidden) {
                    // Page is hidden, clean up any visible loading indicators
                    const $indicator = $('#redco-auto-save-loading');
                    if ($indicator.is(':visible')) {
                        $indicator.fadeOut(200);
                    }
                }
            });
        }

        // Handle window beforeunload to clean up
        $(window).on('beforeunload', function() {
            const $indicator = $('#redco-auto-save-loading');
            if ($indicator.is(':visible')) {
                $indicator.hide();
            }
        });

        // Add keyboard accessibility
        $(document).on('keydown', function(e) {
            // ESC key to dismiss loading indicator if it's been visible too long
            if (e.key === 'Escape') {
                const $indicator = $('#redco-auto-save-loading');
                if ($indicator.is(':visible') && $indicator.hasClass('show')) {
                    // Check if it's been visible for more than 5 seconds
                    const showTime = $indicator.data('show-time');
                    if (showTime && (Date.now() - showTime) > 5000) {
                        $indicator.removeClass('show').addClass('hide');
                        setTimeout(() => {
                            $indicator.fadeOut(200);
                        }, 100);
                    }
                }
            }
        });

        // Track show time for accessibility
        $(document).on('DOMNodeInserted', '#redco-auto-save-loading', function() {
            const $indicator = $(this);
            if ($indicator.hasClass('show')) {
                $indicator.data('show-time', Date.now());
            }
        });

        // Ensure proper cleanup on form submission
        $('form').on('submit', function() {
            const $indicator = $('#redco-auto-save-loading');
            if ($indicator.is(':visible')) {
                $indicator.fadeOut(200);
            }
        });
    });

    // Expose utility functions globally
    window.RedcoLoadingUtils = {
        /**
         * Manually show loading indicator
         */
        show: function(message, module) {
            if (typeof RedcoAutoSave !== 'undefined' && RedcoAutoSave.showLoadingIndicator) {
                RedcoAutoSave.showLoadingIndicator(module || 'settings');
                
                if (message) {
                    const $indicator = $('#redco-auto-save-loading');
                    const $text = $indicator.find('.redco-loading-text');
                    $text.text(message);
                }
            }
        },

        /**
         * Manually hide loading indicator
         */
        hide: function(state, message) {
            if (typeof RedcoAutoSave !== 'undefined' && RedcoAutoSave.hideLoadingIndicator) {
                RedcoAutoSave.hideLoadingIndicator(state, message);
            }
        },

        /**
         * Check if loading indicator is visible
         */
        isVisible: function() {
            const $indicator = $('#redco-auto-save-loading');
            return $indicator.is(':visible');
        },

        /**
         * Force cleanup of loading indicator
         */
        forceCleanup: function() {
            const $indicator = $('#redco-auto-save-loading');
            $indicator.removeClass('show success error').addClass('hide');
            setTimeout(() => {
                $indicator.fadeOut(200, () => {
                    $indicator.removeClass('hide');
                    $indicator.find('.redco-loading-text').text('Saving...');
                    $indicator.attr('aria-label', 'Saving settings');
                });
            }, 100);
        }
    };

})(jQuery);
