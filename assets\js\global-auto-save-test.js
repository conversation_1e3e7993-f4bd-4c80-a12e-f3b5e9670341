/**
 * Global Auto-Save System Test Suite
 * 
 * Comprehensive tests for the new global auto-save functionality
 */

(function($) {
    'use strict';

    window.RedcoGlobalAutoSaveTest = {
        
        // Test configuration
        config: {
            testModule: 'test-module',
            testTimeout: 5000
        },

        // Test results
        results: {
            passed: 0,
            failed: 0,
            tests: []
        },

        // Initialize test suite
        init: function() {
            console.log('🧪 Initializing Global Auto-Save Test Suite...');
            this.createTestEnvironment();
            this.runAllTests();
        },

        // Create test environment
        createTestEnvironment: function() {
            // Create test form if it doesn't exist
            if ($('#redco-auto-save-test-form').length === 0) {
                const testForm = `
                    <div id="redco-auto-save-test-container" style="position: fixed; top: 100px; right: 20px; background: white; border: 1px solid #ccc; padding: 20px; z-index: 9999; width: 300px;">
                        <h4>Auto-Save Test Form</h4>
                        <form id="redco-auto-save-test-form" class="redco-module-form" data-module="${this.config.testModule}">
                            <div class="form-group">
                                <label for="test-text-field">Text Field:</label>
                                <input type="text" id="test-text-field" name="settings[test_text]" value="initial value" />
                            </div>
                            <div class="form-group">
                                <label for="test-checkbox">Checkbox:</label>
                                <input type="checkbox" id="test-checkbox" name="settings[test_checkbox]" value="1" />
                            </div>
                            <div class="form-group">
                                <label for="test-select">Select:</label>
                                <select id="test-select" name="settings[test_select]">
                                    <option value="option1">Option 1</option>
                                    <option value="option2">Option 2</option>
                                    <option value="option3">Option 3</option>
                                </select>
                            </div>
                            <button type="button" id="run-auto-save-tests">Run Tests</button>
                            <button type="button" id="close-test-form">Close</button>
                        </form>
                        <div id="test-results" style="margin-top: 15px; font-size: 12px;"></div>
                    </div>
                `;
                
                $('body').append(testForm);
                
                // Bind close button
                $('#close-test-form').on('click', function() {
                    $('#redco-auto-save-test-container').remove();
                });
                
                // Bind test runner
                $('#run-auto-save-tests').on('click', () => {
                    this.runAllTests();
                });
            }
        },

        // Run all tests
        runAllTests: function() {
            console.log('🚀 Running Global Auto-Save Tests...');
            this.resetResults();
            
            const tests = [
                this.testFormDetection,
                this.testFieldBinding,
                this.testStatusIndicators,
                this.testAutoSaveTriggering,
                this.testNetworkHandling,
                this.testMemoryManagement
            ];

            let testIndex = 0;
            const runNextTest = () => {
                if (testIndex < tests.length) {
                    const test = tests[testIndex];
                    console.log(`Running test: ${test.name}`);
                    
                    try {
                        const result = test.call(this);
                        if (result instanceof Promise) {
                            result.then(() => {
                                testIndex++;
                                setTimeout(runNextTest, 100);
                            }).catch((error) => {
                                this.recordTestResult(test.name, false, error.message);
                                testIndex++;
                                setTimeout(runNextTest, 100);
                            });
                        } else {
                            testIndex++;
                            setTimeout(runNextTest, 100);
                        }
                    } catch (error) {
                        this.recordTestResult(test.name, false, error.message);
                        testIndex++;
                        setTimeout(runNextTest, 100);
                    }
                } else {
                    this.displayResults();
                }
            };

            runNextTest();
        },

        // Test 1: Form Detection
        testFormDetection: function() {
            const testName = 'Form Detection';
            
            try {
                // Check if global auto-save system is initialized
                if (typeof window.RedcoGlobalAutoSave === 'undefined') {
                    throw new Error('RedcoGlobalAutoSave not found');
                }

                // Check if test form is detected
                const $testForm = $('#redco-auto-save-test-form');
                if ($testForm.length === 0) {
                    throw new Error('Test form not found');
                }

                // Check if form has been processed by auto-save system
                const hasAutoSaveFields = $testForm.find('.redco-auto-save-enabled').length > 0;
                if (!hasAutoSaveFields) {
                    // Manually trigger form setup for testing
                    if (window.RedcoGlobalAutoSave.setupFormAutoSave) {
                        window.RedcoGlobalAutoSave.setupFormAutoSave($testForm, this.config.testModule);
                    }
                }

                this.recordTestResult(testName, true, 'Form detection working');
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 2: Field Binding
        testFieldBinding: function() {
            const testName = 'Field Binding';
            
            try {
                const $testFields = $('#redco-auto-save-test-form input, #redco-auto-save-test-form select');
                
                if ($testFields.length === 0) {
                    throw new Error('No test fields found');
                }

                let boundFields = 0;
                $testFields.each(function() {
                    const $field = $(this);
                    if ($field.hasClass('redco-auto-save-enabled')) {
                        boundFields++;
                    }
                });

                if (boundFields === 0) {
                    throw new Error('No fields bound to auto-save');
                }

                this.recordTestResult(testName, true, `${boundFields} fields bound successfully`);
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 3: Status Indicators
        testStatusIndicators: function() {
            const testName = 'Status Indicators';
            
            try {
                const $testFields = $('#redco-auto-save-test-form input, #redco-auto-save-test-form select');
                let indicatorsFound = 0;

                $testFields.each(function() {
                    const $field = $(this);
                    const $indicator = $field.siblings('.redco-auto-save-status');
                    if ($indicator.length > 0) {
                        indicatorsFound++;
                    }
                });

                if (indicatorsFound === 0) {
                    throw new Error('No status indicators found');
                }

                this.recordTestResult(testName, true, `${indicatorsFound} status indicators created`);
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 4: Auto-Save Triggering
        testAutoSaveTriggering: function() {
            const testName = 'Auto-Save Triggering';
            
            return new Promise((resolve, reject) => {
                try {
                    const $textField = $('#test-text-field');
                    if ($textField.length === 0) {
                        throw new Error('Test text field not found');
                    }

                    // Monitor for auto-save activity
                    let autoSaveTriggered = false;
                    const originalAjax = $.ajax;
                    
                    $.ajax = function(options) {
                        if (options.data && options.data.action === 'redco_global_auto_save') {
                            autoSaveTriggered = true;
                            // Restore original ajax
                            $.ajax = originalAjax;
                            // Mock successful response
                            if (options.success) {
                                options.success({
                                    success: true,
                                    data: { message: 'Test save successful' }
                                });
                            }
                            return { abort: function() {} };
                        }
                        return originalAjax.apply(this, arguments);
                    };

                    // Trigger change event
                    $textField.val('test value changed').trigger('change');

                    // Wait for auto-save to trigger
                    setTimeout(() => {
                        $.ajax = originalAjax; // Restore in case it wasn't triggered
                        
                        if (autoSaveTriggered) {
                            this.recordTestResult(testName, true, 'Auto-save triggered successfully');
                        } else {
                            this.recordTestResult(testName, false, 'Auto-save not triggered');
                        }
                        resolve();
                    }, 4000); // Wait longer than debounce delay

                } catch (error) {
                    this.recordTestResult(testName, false, error.message);
                    reject(error);
                }
            });
        },

        // Test 5: Network Handling
        testNetworkHandling: function() {
            const testName = 'Network Handling';
            
            try {
                // Check if network monitoring is set up
                if (typeof window.RedcoGlobalAutoSave.state.isOnline === 'undefined') {
                    throw new Error('Network monitoring not initialized');
                }

                // Check if network status indicator exists
                const $networkStatus = $('.redco-network-status');
                if ($networkStatus.length === 0) {
                    // This is okay, indicator is created when needed
                }

                this.recordTestResult(testName, true, 'Network handling initialized');
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Test 6: Memory Management
        testMemoryManagement: function() {
            const testName = 'Memory Management';
            
            try {
                // Check if memory management functions exist
                if (typeof window.RedcoGlobalAutoSave.config.memoryThreshold === 'undefined') {
                    throw new Error('Memory threshold not configured');
                }

                // Check if state management is working
                if (typeof window.RedcoGlobalAutoSave.state.saveTimers === 'undefined') {
                    throw new Error('State management not initialized');
                }

                this.recordTestResult(testName, true, 'Memory management configured');
            } catch (error) {
                this.recordTestResult(testName, false, error.message);
            }
        },

        // Record test result
        recordTestResult: function(testName, passed, message) {
            this.results.tests.push({
                name: testName,
                passed: passed,
                message: message
            });

            if (passed) {
                this.results.passed++;
                console.log(`✅ ${testName}: ${message}`);
            } else {
                this.results.failed++;
                console.log(`❌ ${testName}: ${message}`);
            }
        },

        // Reset test results
        resetResults: function() {
            this.results = {
                passed: 0,
                failed: 0,
                tests: []
            };
        },

        // Display test results
        displayResults: function() {
            const total = this.results.passed + this.results.failed;
            const passRate = total > 0 ? Math.round((this.results.passed / total) * 100) : 0;
            
            console.log(`\n🧪 Global Auto-Save Test Results:`);
            console.log(`Total Tests: ${total}`);
            console.log(`Passed: ${this.results.passed}`);
            console.log(`Failed: ${this.results.failed}`);
            console.log(`Pass Rate: ${passRate}%`);

            // Update UI
            const $resultsDiv = $('#test-results');
            if ($resultsDiv.length > 0) {
                let resultsHtml = `<strong>Test Results:</strong><br>`;
                resultsHtml += `Passed: ${this.results.passed}, Failed: ${this.results.failed}<br>`;
                
                this.results.tests.forEach(test => {
                    const icon = test.passed ? '✅' : '❌';
                    resultsHtml += `${icon} ${test.name}<br>`;
                });
                
                $resultsDiv.html(resultsHtml);
            }

            if (this.results.failed === 0) {
                console.log('🎉 All tests passed!');
            } else {
                console.log('⚠️ Some tests failed. Check the details above.');
            }
        }
    };

    // Auto-run tests in debug mode
    $(document).ready(function() {
        if (window.location.href.includes('debug=1') || window.location.href.includes('test=auto-save')) {
            setTimeout(() => {
                RedcoGlobalAutoSaveTest.init();
            }, 2000); // Wait for auto-save system to initialize
        }
    });

})(jQuery);
