/**
 * Force Loading Test
 * 
 * Forces the loading indicator to show for testing purposes
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Wait for page to load
        setTimeout(function() {
            console.log('🚀 Force loading test starting...');
            
            // Force create the loading indicator if it doesn't exist
            if ($('#redco-auto-save-loading').length === 0) {
                console.log('🔧 Creating loading indicator manually...');
                
                const loadingHtml = `
                    <div id="redco-auto-save-loading" class="redco-loading-indicator" style="position: fixed; top: 20px; right: 20px; background: rgba(255, 255, 255, 0.95); border: 1px solid #ddd; border-radius: 6px; padding: 8px 12px; display: none; align-items: center; gap: 8px; font-size: 13px; color: #333; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); z-index: 999999;">
                        <div class="redco-loading-spinner" style="display: flex; align-items: center; justify-content: center;">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style="animation: spin 1s linear infinite;">
                                <circle cx="8" cy="8" r="6" stroke="#0073aa" stroke-width="2" stroke-linecap="round" stroke-dasharray="9.42" stroke-dashoffset="9.42">
                                    <animateTransform attributeName="transform" dur="1s" type="rotate" values="0 8 8;360 8 8" repeatCount="indefinite"/>
                                    <animate attributeName="stroke-dashoffset" dur="1s" values="9.42;0;9.42" repeatCount="indefinite"/>
                                </circle>
                            </svg>
                        </div>
                        <span class="redco-loading-text">Saving...</span>
                    </div>
                `;
                
                $('body').append(loadingHtml);
                
                // Add CSS animation
                if ($('#force-loading-styles').length === 0) {
                    const styles = `
                        <style id="force-loading-styles">
                        @keyframes spin {
                            from { transform: rotate(0deg); }
                            to { transform: rotate(360deg); }
                        }
                        </style>
                    `;
                    $('head').append(styles);
                }
            }
            
            // Add test buttons
            const testButtons = `
                <div id="loading-test-controls" style="position: fixed; bottom: 20px; left: 20px; z-index: 999999; display: flex; gap: 10px; flex-direction: column;">
                    <button id="show-loading-btn" style="background: #0073aa; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                        Show Loading
                    </button>
                    <button id="hide-loading-btn" style="background: #dc3545; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                        Hide Loading
                    </button>
                    <button id="test-auto-save-btn" style="background: #28a745; color: white; border: none; padding: 10px 15px; border-radius: 5px; cursor: pointer; font-size: 12px;">
                        Test Auto-Save
                    </button>
                </div>
            `;
            
            $('body').append(testButtons);
            
            // Bind test button events
            $('#show-loading-btn').on('click', function() {
                console.log('🧪 Manual show loading test');
                const $indicator = $('#redco-auto-save-loading');
                $indicator.css('display', 'flex').fadeIn(200);
                console.log('Loading indicator should be visible now');
            });
            
            $('#hide-loading-btn').on('click', function() {
                console.log('🧪 Manual hide loading test');
                const $indicator = $('#redco-auto-save-loading');
                $indicator.fadeOut(200);
            });
            
            $('#test-auto-save-btn').on('click', function() {
                console.log('🧪 Testing auto-save trigger');
                
                // Try to trigger auto-save on the first form field we find
                const $firstInput = $('.redco-module-form input, .redco-module-form select, .redco-module-form textarea').first();
                if ($firstInput.length > 0) {
                    console.log('Found input field, triggering change event');
                    $firstInput.trigger('change');
                } else {
                    console.log('No form fields found, showing loading manually');
                    $('#show-loading-btn').click();
                }
            });
            
            console.log('✅ Force loading test setup complete');
            
        }, 2000);
    });

})(jQuery);
